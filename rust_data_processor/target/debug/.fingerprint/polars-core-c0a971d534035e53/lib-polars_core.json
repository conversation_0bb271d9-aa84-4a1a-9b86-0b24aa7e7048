{"rustc": 11410426090777951712, "features": "[\"algorithm_group_by\", \"chrono\", \"comfy-table\", \"docs\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-time\", \"fmt\", \"lazy\", \"rand\", \"rand_distr\", \"random\", \"regex\", \"rows\", \"strings\", \"temporal\", \"zip_with\"]", "declared_features": "[\"algorithm_group_by\", \"arrow-array\", \"arrow_rs\", \"asof_join\", \"avx512\", \"bigidx\", \"checked_arithmetic\", \"chrono\", \"chrono-tz\", \"chunked_ids\", \"comfy-table\", \"dataframe_arithmetic\", \"default\", \"describe\", \"diagonal_concat\", \"docs\", \"docs-selection\", \"dot_product\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"fmt\", \"fmt_no_tty\", \"group_by_list\", \"horizontal_concat\", \"is_first_distinct\", \"is_last_distinct\", \"lazy\", \"ndarray\", \"nightly\", \"object\", \"partition_by\", \"performant\", \"product\", \"python\", \"rand\", \"rand_distr\", \"random\", \"regex\", \"reinterpret\", \"rolling_window\", \"round_series\", \"row_hash\", \"rows\", \"serde\", \"serde-lazy\", \"serde_json\", \"simd\", \"strings\", \"take_opt_iter\", \"temporal\", \"timezones\", \"unique_counts\", \"zip_with\"]", "target": 16956922054172295634, "profile": 2241668132362809309, "path": 14627378796559697219, "deps": [[966925859616469517, "ahash", false, 10174633265036692475], [1458751047317679729, "build_script_build", false, 8528043991932787619], [1804806304303030865, "xxhash_rust", false, 13935977803782250976], [2752773142256470517, "arrow", false, 1554403704926783477], [3419428956812390430, "smartstring", false, 9692665678905392005], [3722963349756955755, "once_cell", false, 6293031810255931828], [5157631553186200874, "num_traits", false, 2242811736461817682], [6493259146304816786, "indexmap", false, 15577605399107965964], [6511429716036861196, "bytemuck", false, 639825653603922915], [6643084693527522471, "polars_utils", false, 9121758026600700075], [7301041063585579719, "polars_row", false, 5481922927869237404], [7896293946984509699, "bitflags", false, 10938229885740913058], [8008191657135824715, "thiserror", false, 2068976673547303746], [9196727883430091646, "rand_distr", false, 18169855128112281724], [9451456094439810778, "regex", false, 16501748659867266115], [9897246384292347999, "chrono", false, 5502450951822104167], [10102053495671323854, "polars_compute", false, 3549654717666285839], [10170320114020213262, "comfy_table", false, 15641605986405809936], [10697383615564341592, "rayon", false, 9537286647097859485], [11967285383858598289, "polars_error", false, 15057136308127966918], [12170264697963848012, "either", false, 501776948521816246], [13018563866916002725, "hashbrown", false, 17510630187605862833], [13208667028893622512, "rand", false, 1928496208167558564]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-core-c0a971d534035e53/dep-lib-polars_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}