{"rustc": 11410426090777951712, "features": "[\"csv\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-time\", \"dynamic_group_by\", \"polars-time\", \"strings\", \"temporal\"]", "declared_features": "[\"abs\", \"approx_unique\", \"arg_where\", \"asof_join\", \"async\", \"bigidx\", \"binary_encoding\", \"chunked_ids\", \"cloud\", \"cloud_write\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cumulative_eval\", \"cutqcut\", \"date_offset\", \"diagonal_concat\", \"diff\", \"dot_diagram\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"extract_groups\", \"extract_jsonpath\", \"fmt\", \"fused\", \"future\", \"futures\", \"interpolate\", \"ipc\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_eval\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"nightly\", \"object\", \"panic_on_schema\", \"parquet\", \"pct_change\", \"peaks\", \"pivot\", \"polars-json\", \"polars-pipe\", \"polars-time\", \"propagate_nans\", \"pyo3\", \"python\", \"random\", \"range\", \"rank\", \"regex\", \"repeat_by\", \"rle\", \"rolling_window\", \"round_series\", \"row_hash\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"sign\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_to_integer\", \"strings\", \"temporal\", \"test\", \"test_all\", \"timezones\", \"tokio\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\"]", "target": 6595311888478621261, "profile": 2241668132362809309, "path": 6553322699556129199, "deps": [[963741888112789140, "polars_ops", false, 15182148848856118304], [966925859616469517, "ahash", false, 10174633265036692475], [3419428956812390430, "smartstring", false, 9692665678905392005], [3495776386425160289, "polars_plan", false, 16968808509343600660], [3722963349756955755, "once_cell", false, 6293031810255931828], [4398657206787234709, "build_script_build", false, 9771704514353535373], [4446769819467865508, "polars_core", false, 17631233393019503896], [4978756888369265998, "polars_utils", false, 15069460263289402904], [6565397770530258860, "polars_io", false, 1022086969053575509], [6930142607113040362, "arrow", false, 17451091832504977821], [7896293946984509699, "bitflags", false, 10938229885740913058], [10697383615564341592, "rayon", false, 9537286647097859485], [11411182522717811070, "polars_time", false, 3684017373661468662], [17155886227862585100, "glob", false, 14943669274292680279]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-lazy-b6c3e56c4c2bacf5/dep-lib-polars_lazy", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}