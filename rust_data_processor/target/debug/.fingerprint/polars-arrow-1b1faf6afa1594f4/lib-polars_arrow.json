{"rustc": 11410426090777951712, "features": "[\"atoi_simd\", \"compute\", \"compute_aggregate\", \"compute_arithmetics\", \"compute_arithmetics_decimal\", \"compute_bitwise\", \"compute_boolean\", \"compute_boolean_kleene\", \"compute_cast\", \"compute_comparison\", \"compute_concatenate\", \"compute_filter\", \"compute_hash\", \"compute_if_then_else\", \"compute_take\", \"compute_temporal\", \"fast-float\", \"itoa\", \"multiversion\", \"ryu\", \"strength_reduce\", \"strings\", \"temporal\"]", "declared_features": "[\"arrow-array\", \"arrow-buffer\", \"arrow-data\", \"arrow-format\", \"arrow-schema\", \"arrow_rs\", \"async-stream\", \"atoi\", \"atoi_simd\", \"avro-schema\", \"bigidx\", \"chrono-tz\", \"compute\", \"compute_aggregate\", \"compute_arithmetics\", \"compute_arithmetics_decimal\", \"compute_bitwise\", \"compute_boolean\", \"compute_boolean_kleene\", \"compute_cast\", \"compute_comparison\", \"compute_concatenate\", \"compute_filter\", \"compute_hash\", \"compute_if_then_else\", \"compute_take\", \"compute_temporal\", \"default\", \"dtype-array\", \"dtype-decimal\", \"fast-float\", \"full\", \"futures\", \"hex\", \"indexmap\", \"io_avro\", \"io_avro_async\", \"io_avro_compression\", \"io_flight\", \"io_ipc\", \"io_ipc_compression\", \"io_ipc_read_async\", \"io_ipc_write_async\", \"itoa\", \"lz4\", \"multiversion\", \"nightly\", \"performant\", \"regex\", \"regex-syntax\", \"ryu\", \"serde\", \"simd\", \"strength_reduce\", \"strings\", \"temporal\", \"timezones\", \"zstd\"]", "target": 8727143522642269072, "profile": 2241668132362809309, "path": 858049732250124234, "deps": [[966925859616469517, "ahash", false, 10174633265036692475], [1216309103264968120, "ryu", false, 1451197909508611192], [3726277658779405417, "strength_reduce", false, 15573166585638413043], [4978756888369265998, "polars_utils", false, 15069460263289402904], [5157631553186200874, "num_traits", false, 2242811736461817682], [6096440479827326274, "multiversion", false, 14724056219504139809], [6511429716036861196, "bytemuck", false, 639825653603922915], [7695812897323945497, "itoa", false, 17258409771626943784], [7898571650830454567, "ethnum", false, 13818054456629799990], [8067010153367330186, "simdutf8", false, 12757751879509257494], [9122563107207267705, "dyn_clone", false, 11555960132134616454], [9235208004366183979, "streaming_iterator", false, 13050830693502628241], [9897246384292347999, "chrono", false, 5502450951822104167], [9938377527623146791, "foreign_vec", false, 126413623813350216], [10766548186203220745, "polars_error", false, 10971451950184320256], [12170264697963848012, "either", false, 501776948521816246], [13018563866916002725, "hashbrown", false, 17510630187605862833], [16583050384810566368, "atoi_simd", false, 48418463603378939], [17433304786843909481, "fast_float", false, 17936471312584981539]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-arrow-1b1faf6afa1594f4/dep-lib-polars_arrow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}