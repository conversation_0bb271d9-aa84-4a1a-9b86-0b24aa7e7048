{"rustc": 11410426090777951712, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\", \"rayon\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 15657897354478470176, "path": 17010700139677128289, "deps": [[966925859616469517, "ahash", false, 4020335100232093750], [9150530836556604396, "allocator_api2", false, 16814756329056415075], [10697383615564341592, "rayon", false, 17457214322954202663]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-dac5ac8621823635/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}