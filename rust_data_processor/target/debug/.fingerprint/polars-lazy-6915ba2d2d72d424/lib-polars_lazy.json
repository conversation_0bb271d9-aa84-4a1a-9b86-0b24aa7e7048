{"rustc": 11410426090777951712, "features": "[\"csv\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-time\", \"dynamic_group_by\", \"polars-time\", \"strings\", \"temporal\"]", "declared_features": "[\"abs\", \"approx_unique\", \"arg_where\", \"asof_join\", \"async\", \"bigidx\", \"binary_encoding\", \"chunked_ids\", \"cloud\", \"cloud_write\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cumulative_eval\", \"cutqcut\", \"date_offset\", \"diagonal_concat\", \"diff\", \"dot_diagram\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"extract_groups\", \"extract_jsonpath\", \"fmt\", \"fused\", \"future\", \"futures\", \"interpolate\", \"ipc\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_eval\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"nightly\", \"object\", \"panic_on_schema\", \"parquet\", \"pct_change\", \"peaks\", \"pivot\", \"polars-json\", \"polars-pipe\", \"polars-time\", \"propagate_nans\", \"pyo3\", \"python\", \"random\", \"range\", \"rank\", \"regex\", \"repeat_by\", \"rle\", \"rolling_window\", \"round_series\", \"row_hash\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"sign\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_to_integer\", \"strings\", \"temporal\", \"test\", \"test_all\", \"timezones\", \"tokio\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\"]", "target": 6595311888478621261, "profile": 15657897354478470176, "path": 6553322699556129199, "deps": [[963741888112789140, "polars_ops", false, 12345948248735369730], [966925859616469517, "ahash", false, 4020335100232093750], [3419428956812390430, "smartstring", false, 16825798558610601556], [3495776386425160289, "polars_plan", false, 18014379667480210767], [3722963349756955755, "once_cell", false, 17804875059537849432], [4398657206787234709, "build_script_build", false, 9771704514353535373], [4446769819467865508, "polars_core", false, 8572961208274694123], [4978756888369265998, "polars_utils", false, 8507928368020970212], [6565397770530258860, "polars_io", false, 10871352381460799255], [6930142607113040362, "arrow", false, 2698406127485069570], [7896293946984509699, "bitflags", false, 9316908621467576491], [10697383615564341592, "rayon", false, 17457214322954202663], [11411182522717811070, "polars_time", false, 2891818763283882028], [17155886227862585100, "glob", false, 517364648419503531]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-lazy-6915ba2d2d72d424/dep-lib-polars_lazy", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}