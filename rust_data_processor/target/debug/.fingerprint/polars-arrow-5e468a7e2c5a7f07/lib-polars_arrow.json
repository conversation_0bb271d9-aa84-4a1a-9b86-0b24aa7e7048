{"rustc": 11410426090777951712, "features": "[\"atoi_simd\", \"compute\", \"compute_aggregate\", \"compute_arithmetics\", \"compute_arithmetics_decimal\", \"compute_bitwise\", \"compute_boolean\", \"compute_boolean_kleene\", \"compute_cast\", \"compute_comparison\", \"compute_concatenate\", \"compute_filter\", \"compute_hash\", \"compute_if_then_else\", \"compute_take\", \"compute_temporal\", \"fast-float\", \"itoa\", \"multiversion\", \"ryu\", \"strength_reduce\", \"strings\", \"temporal\"]", "declared_features": "[\"arrow-array\", \"arrow-buffer\", \"arrow-data\", \"arrow-format\", \"arrow-schema\", \"arrow_rs\", \"async-stream\", \"atoi\", \"atoi_simd\", \"avro-schema\", \"bigidx\", \"chrono-tz\", \"compute\", \"compute_aggregate\", \"compute_arithmetics\", \"compute_arithmetics_decimal\", \"compute_bitwise\", \"compute_boolean\", \"compute_boolean_kleene\", \"compute_cast\", \"compute_comparison\", \"compute_concatenate\", \"compute_filter\", \"compute_hash\", \"compute_if_then_else\", \"compute_take\", \"compute_temporal\", \"default\", \"dtype-array\", \"dtype-decimal\", \"fast-float\", \"full\", \"futures\", \"hex\", \"indexmap\", \"io_avro\", \"io_avro_async\", \"io_avro_compression\", \"io_flight\", \"io_ipc\", \"io_ipc_compression\", \"io_ipc_read_async\", \"io_ipc_write_async\", \"itoa\", \"lz4\", \"multiversion\", \"nightly\", \"performant\", \"regex\", \"regex-syntax\", \"ryu\", \"serde\", \"simd\", \"strength_reduce\", \"strings\", \"temporal\", \"timezones\", \"zstd\"]", "target": 8727143522642269072, "profile": 15657897354478470176, "path": 2054032496103895621, "deps": [[966925859616469517, "ahash", false, 4020335100232093750], [1216309103264968120, "ryu", false, 13178071118341633384], [2752773142256470517, "build_script_build", false, 13430607446412136243], [3726277658779405417, "strength_reduce", false, 16087362682574334358], [5157631553186200874, "num_traits", false, 1532232505406381417], [6096440479827326274, "multiversion", false, 10390452838160784219], [6511429716036861196, "bytemuck", false, 1177516685954284277], [6643084693527522471, "polars_utils", false, 1252696596914666061], [7695812897323945497, "itoa", false, 8274899767785620422], [7898571650830454567, "ethnum", false, 16194771718873307691], [8067010153367330186, "simdutf8", false, 4843188865726315609], [9122563107207267705, "dyn_clone", false, 13800550729294524803], [9235208004366183979, "streaming_iterator", false, 10034070317304021844], [9897246384292347999, "chrono", false, 11771647821370589633], [9938377527623146791, "foreign_vec", false, 11366717994902584556], [11967285383858598289, "polars_error", false, 7666834132261226001], [12170264697963848012, "either", false, 6490692808720858043], [13018563866916002725, "hashbrown", false, 10905555190870098526], [16583050384810566368, "atoi_simd", false, 10285584389567605747], [17433304786843909481, "fast_float", false, 10416057100999416633]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-arrow-5e468a7e2c5a7f07/dep-lib-polars_arrow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}