{"rustc": 11410426090777951712, "features": "[\"csv\", \"default\", \"docs\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-slim\", \"dynamic_group_by\", \"fmt\", \"lazy\", \"polars-io\", \"polars-lazy\", \"polars-time\", \"strings\", \"temporal\", \"zip_with\"]", "declared_features": "[\"abs\", \"algo\", \"approx_unique\", \"arg_where\", \"asof_join\", \"async\", \"avro\", \"avx512\", \"aws\", \"azure\", \"bench\", \"bigidx\", \"binary_encoding\", \"checked_arithmetic\", \"chunked_ids\", \"cloud\", \"cloud_write\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cumulative_eval\", \"cutqcut\", \"dataframe_arithmetic\", \"date_offset\", \"decompress\", \"decompress-fast\", \"default\", \"describe\", \"diagonal_concat\", \"diff\", \"docs\", \"docs-selection\", \"dot_diagram\", \"dot_product\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-full\", \"dtype-i16\", \"dtype-i8\", \"dtype-slim\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"extract_groups\", \"extract_jsonpath\", \"fmt\", \"fmt_no_tty\", \"fused\", \"gcp\", \"group_by_list\", \"horizontal_concat\", \"http\", \"interpolate\", \"ipc\", \"ipc_streaming\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"lazy\", \"lazy_regex\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_eval\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"ndarray\", \"nightly\", \"object\", \"parquet\", \"partition_by\", \"pct_change\", \"peaks\", \"performant\", \"pivot\", \"polars-algo\", \"polars-io\", \"polars-lazy\", \"polars-sql\", \"polars-time\", \"product\", \"propagate_nans\", \"random\", \"range\", \"rank\", \"reinterpret\", \"repeat_by\", \"rle\", \"rolling_window\", \"round_series\", \"row_hash\", \"rows\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"serde-lazy\", \"sign\", \"simd\", \"sort_multiple\", \"sql\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_to_integer\", \"strings\", \"take_opt_iter\", \"temporal\", \"test\", \"timezones\", \"to_dummies\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\", \"zip_with\"]", "target": 9043257715589065068, "profile": 2241668132362809309, "path": 2102841047435524234, "deps": [[963741888112789140, "polars_ops", false, 15182148848856118304], [2597253916522265657, "build_script_build", false, 2824302444109663733], [4398657206787234709, "polars_lazy", false, 15136364136053141546], [4446769819467865508, "polars_core", false, 17631233393019503896], [6565397770530258860, "polars_io", false, 1022086969053575509], [11411182522717811070, "polars_time", false, 3684017373661468662]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-1f291f6cae73a8b1/dep-lib-polars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}