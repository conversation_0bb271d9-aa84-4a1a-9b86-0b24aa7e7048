{"rustc": 11410426090777951712, "features": "[]", "declared_features": "[\"abs\", \"approx_unique\", \"asof_join\", \"base64\", \"big_idx\", \"binary_encoding\", \"chrono\", \"chrono-tz\", \"chunked_ids\", \"convert_index\", \"cov\", \"cross_join\", \"cum_agg\", \"cutqcut\", \"diff\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"ewma\", \"extract_groups\", \"extract_jsonpath\", \"fused\", \"group_by_list\", \"hash\", \"hex\", \"interpolate\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"jsonpath_lib\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"mode\", \"moment\", \"nightly\", \"object\", \"pct_change\", \"peaks\", \"performant\", \"pivot\", \"polars-json\", \"propagate_nans\", \"rand\", \"rand_distr\", \"random\", \"rank\", \"repeat_by\", \"rle\", \"rolling_window\", \"round_series\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"serde_json\", \"simd\", \"string_encoding\", \"string_pad\", \"string_to_integer\", \"strings\", \"timezones\", \"to_dummies\", \"top_k\", \"unique_counts\", \"zip_with\"]", "target": 13897111239935570622, "profile": 15657897354478470176, "path": 13452432681061460096, "deps": [[963741888112789140, "build_script_build", false, 14025896783521469968], [966925859616469517, "ahash", false, 4020335100232093750], [3419428956812390430, "smartstring", false, 16825798558610601556], [4446769819467865508, "polars_core", false, 14700372245164784880], [4978756888369265998, "polars_utils", false, 1073809846642864966], [5157631553186200874, "num_traits", false, 1532232505406381417], [6493259146304816786, "indexmap", false, 2733906120469181723], [6511429716036861196, "bytemuck", false, 1177516685954284277], [6930142607113040362, "arrow", false, 3871603002785080498], [9451456094439810778, "regex", false, 6337596358325284129], [10697383615564341592, "rayon", false, 17457214322954202663], [10766548186203220745, "polars_error", false, 8034755695549783337], [12084198568646017729, "arg<PERSON><PERSON>", false, 10488530148919971650], [12170264697963848012, "either", false, 6490692808720858043], [13018563866916002725, "hashbrown", false, 10905555190870098526], [15932120279885307830, "memchr", false, 14643597465583377262]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-ops-c1a5fc2c4780a41a/dep-lib-polars_ops", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}