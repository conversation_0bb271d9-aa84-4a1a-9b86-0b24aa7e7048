{"rustc": 11410426090777951712, "features": "[\"regex\"]", "declared_features": "[\"arrow-format\", \"avro-schema\", \"object_store\", \"python\", \"regex\"]", "target": 9265665750937118341, "profile": 15657897354478470176, "path": 9803550764400168991, "deps": [[8008191657135824715, "thiserror", false, 1048344762606963720], [8067010153367330186, "simdutf8", false, 4843188865726315609], [9451456094439810778, "regex", false, 6337596358325284129]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-error-e0765784ca4513fd/dep-lib-polars_error", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}