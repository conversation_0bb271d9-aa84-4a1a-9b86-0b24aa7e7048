{"rustc": 11410426090777951712, "features": "[\"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"strings\"]", "declared_features": "[\"abs\", \"aho-corasick\", \"approx_unique\", \"array_any_all\", \"asof_join\", \"base64\", \"big_idx\", \"binary_encoding\", \"chrono\", \"chrono-tz\", \"chunked_ids\", \"cov\", \"cross_join\", \"cum_agg\", \"cutqcut\", \"diff\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"ewma\", \"extract_groups\", \"extract_jsonpath\", \"find_many\", \"fused\", \"gather\", \"group_by_list\", \"hash\", \"hex\", \"hist\", \"interpolate\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"jsonpath_lib\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"mode\", \"moment\", \"nightly\", \"object\", \"pct_change\", \"peaks\", \"performant\", \"pivot\", \"polars-json\", \"propagate_nans\", \"rand\", \"rand_distr\", \"random\", \"rank\", \"repeat_by\", \"replace\", \"rle\", \"rolling_window\", \"round_series\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"serde_json\", \"simd\", \"string_encoding\", \"string_pad\", \"string_reverse\", \"string_to_integer\", \"strings\", \"timezones\", \"to_dummies\", \"top_k\", \"unicode-reverse\", \"unique_counts\"]", "target": 13897111239935570622, "profile": 2241668132362809309, "path": 13384540683141485528, "deps": [[966925859616469517, "ahash", false, 10174633265036692475], [1458751047317679729, "polars_core", false, 12785791926672904966], [2752773142256470517, "arrow", false, 1554403704926783477], [3419428956812390430, "smartstring", false, 9692665678905392005], [5157631553186200874, "num_traits", false, 2242811736461817682], [6493259146304816786, "indexmap", false, 15577605399107965964], [6511429716036861196, "bytemuck", false, 639825653603922915], [6643084693527522471, "polars_utils", false, 9121758026600700075], [9101573641635189135, "build_script_build", false, 2944134255412683275], [9451456094439810778, "regex", false, 16501748659867266115], [10102053495671323854, "polars_compute", false, 3549654717666285839], [10697383615564341592, "rayon", false, 9537286647097859485], [11967285383858598289, "polars_error", false, 15057136308127966918], [12084198568646017729, "arg<PERSON><PERSON>", false, 767771310210517364], [12170264697963848012, "either", false, 501776948521816246], [13018563866916002725, "hashbrown", false, 17510630187605862833], [15932120279885307830, "memchr", false, 18411148524280002855]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-ops-e644f05f0f35b5ef/dep-lib-polars_ops", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}