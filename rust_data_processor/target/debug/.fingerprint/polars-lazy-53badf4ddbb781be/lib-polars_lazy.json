{"rustc": 11410426090777951712, "features": "[\"csv\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-time\", \"polars-time\", \"strings\", \"temporal\"]", "declared_features": "[\"abs\", \"approx_unique\", \"arg_where\", \"array_any_all\", \"asof_join\", \"async\", \"bigidx\", \"binary_encoding\", \"chunked_ids\", \"cloud\", \"cloud_write\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cumulative_eval\", \"cutqcut\", \"date_offset\", \"diagonal_concat\", \"diff\", \"dot_diagram\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"extract_groups\", \"extract_jsonpath\", \"fmt\", \"fused\", \"future\", \"futures\", \"hist\", \"horizontal_concat\", \"interpolate\", \"ipc\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_eval\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"nightly\", \"object\", \"panic_on_schema\", \"parquet\", \"pct_change\", \"peaks\", \"pivot\", \"polars-json\", \"polars-pipe\", \"polars-time\", \"propagate_nans\", \"pyo3\", \"python\", \"random\", \"range\", \"rank\", \"regex\", \"repeat_by\", \"replace\", \"rle\", \"rolling_window\", \"round_series\", \"row_hash\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"sign\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_reverse\", \"string_to_integer\", \"strings\", \"temporal\", \"test\", \"test_all\", \"timezones\", \"tokio\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\"]", "target": 6595311888478621261, "profile": 2241668132362809309, "path": 280252292503264495, "deps": [[966925859616469517, "ahash", false, 10174633265036692475], [1458751047317679729, "polars_core", false, 12785791926672904966], [2493041755969688708, "polars_plan", false, 10660036100285815575], [2752773142256470517, "arrow", false, 1554403704926783477], [3419428956812390430, "smartstring", false, 9692665678905392005], [3722963349756955755, "once_cell", false, 6293031810255931828], [6643084693527522471, "polars_utils", false, 9121758026600700075], [6715185521598727875, "polars_io", false, 10100491225361608225], [7896293946984509699, "bitflags", false, 10938229885740913058], [9101573641635189135, "polars_ops", false, 10848656423973692580], [10697383615564341592, "rayon", false, 9537286647097859485], [13238314574749385273, "polars_time", false, 3265851153154620215], [15364890382109803616, "build_script_build", false, 13899913472565680515], [17155886227862585100, "glob", false, 14943669274292680279]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-lazy-53badf4ddbb781be/dep-lib-polars_lazy", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}