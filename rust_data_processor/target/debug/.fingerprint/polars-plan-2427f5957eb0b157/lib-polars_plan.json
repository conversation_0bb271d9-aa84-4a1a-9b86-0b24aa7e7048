{"rustc": 11410426090777951712, "features": "[\"csv\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-time\", \"dynamic_group_by\", \"polars-time\", \"strings\", \"temporal\"]", "declared_features": "[\"abs\", \"approx_unique\", \"arg_where\", \"asof_join\", \"async\", \"bigidx\", \"binary_encoding\", \"chrono\", \"chrono-tz\", \"chunked_ids\", \"ciborium\", \"cloud\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cutqcut\", \"date_offset\", \"debugging\", \"diff\", \"dot_diagram\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"extract_groups\", \"extract_jsonpath\", \"ffi_plugin\", \"fmt\", \"fused\", \"future\", \"futures\", \"hive_partitions\", \"interpolate\", \"ipc\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"libloading\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"nightly\", \"object\", \"panic_on_schema\", \"parquet\", \"pct_change\", \"peaks\", \"pivot\", \"polars-ffi\", \"polars-parquet\", \"polars-time\", \"propagate_nans\", \"python\", \"random\", \"range\", \"rank\", \"regex\", \"repeat_by\", \"rle\", \"rolling_window\", \"round_series\", \"row_hash\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"sign\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_to_integer\", \"strings\", \"temporal\", \"timezones\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\"]", "target": 11334077754402081686, "profile": 15657897354478470176, "path": 17582501308129811200, "deps": [[40386456601120721, "percent_encoding", false, 14724578387685143800], [963741888112789140, "polars_ops", false, 12345948248735369730], [966925859616469517, "ahash", false, 4020335100232093750], [3419428956812390430, "smartstring", false, 16825798558610601556], [3495776386425160289, "build_script_build", false, 16765522723528835327], [3722963349756955755, "once_cell", false, 17804875059537849432], [4446769819467865508, "polars_core", false, 8572961208274694123], [4978756888369265998, "polars_utils", false, 8507928368020970212], [6511429716036861196, "bytemuck", false, 1177516685954284277], [6565397770530258860, "polars_io", false, 10871352381460799255], [6930142607113040362, "arrow", false, 2698406127485069570], [10697383615564341592, "rayon", false, 17457214322954202663], [11411182522717811070, "polars_time", false, 2891818763283882028], [15285011755229917804, "strum_macros", false, 4679427528150290646]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-plan-2427f5957eb0b157/dep-lib-polars_plan", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}