{"rustc": 11410426090777951712, "features": "[\"csv\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-time\", \"polars-time\", \"strings\", \"temporal\"]", "declared_features": "[\"abs\", \"approx_unique\", \"arg_where\", \"array_any_all\", \"asof_join\", \"async\", \"bigidx\", \"binary_encoding\", \"chrono\", \"chrono-tz\", \"chunked_ids\", \"ciborium\", \"cloud\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cutqcut\", \"date_offset\", \"debugging\", \"diff\", \"dot_diagram\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"extract_groups\", \"extract_jsonpath\", \"ffi_plugin\", \"find_many\", \"fmt\", \"fused\", \"future\", \"futures\", \"hist\", \"hive_partitions\", \"horizontal_concat\", \"interpolate\", \"ipc\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"libloading\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"nightly\", \"object\", \"panic_on_schema\", \"parquet\", \"pct_change\", \"peaks\", \"pivot\", \"polars-ffi\", \"polars-json\", \"polars-parquet\", \"polars-time\", \"propagate_nans\", \"python\", \"random\", \"range\", \"rank\", \"regex\", \"repeat_by\", \"replace\", \"rle\", \"rolling_window\", \"round_series\", \"row_hash\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"sign\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_reverse\", \"string_to_integer\", \"strings\", \"temporal\", \"timezones\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\"]", "target": 11334077754402081686, "profile": 15657897354478470176, "path": 1900030615374729713, "deps": [[40386456601120721, "percent_encoding", false, 14724578387685143800], [966925859616469517, "ahash", false, 4020335100232093750], [1458751047317679729, "polars_core", false, 963042132233614605], [2493041755969688708, "build_script_build", false, 4455948447774639010], [2752773142256470517, "arrow", false, 4827659178917990186], [3419428956812390430, "smartstring", false, 16825798558610601556], [3722963349756955755, "once_cell", false, 17804875059537849432], [6511429716036861196, "bytemuck", false, 1177516685954284277], [6643084693527522471, "polars_utils", false, 1252696596914666061], [6715185521598727875, "polars_io", false, 2782284420356540327], [9101573641635189135, "polars_ops", false, 14525502535905877367], [10697383615564341592, "rayon", false, 17457214322954202663], [13238314574749385273, "polars_time", false, 17498489232097531914], [15285011755229917804, "strum_macros", false, 4679427528150290646]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-plan-d6d00ce4fdfe83cb/dep-lib-polars_plan", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}