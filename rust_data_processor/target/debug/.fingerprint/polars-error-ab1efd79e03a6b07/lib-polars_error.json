{"rustc": 11410426090777951712, "features": "[\"regex\"]", "declared_features": "[\"arrow-format\", \"avro-schema\", \"object_store\", \"python\", \"regex\"]", "target": 9265665750937118341, "profile": 2241668132362809309, "path": 9803550764400168991, "deps": [[8008191657135824715, "thiserror", false, 2068976673547303746], [8067010153367330186, "simdutf8", false, 12757751879509257494], [9451456094439810778, "regex", false, 16501748659867266115]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-error-ab1efd79e03a6b07/dep-lib-polars_error", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}