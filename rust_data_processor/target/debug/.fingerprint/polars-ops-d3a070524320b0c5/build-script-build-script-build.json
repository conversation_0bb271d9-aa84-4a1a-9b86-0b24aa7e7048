{"rustc": 11410426090777951712, "features": "[\"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"strings\", \"zip_with\"]", "declared_features": "[\"abs\", \"approx_unique\", \"asof_join\", \"base64\", \"big_idx\", \"binary_encoding\", \"chrono\", \"chrono-tz\", \"chunked_ids\", \"convert_index\", \"cov\", \"cross_join\", \"cum_agg\", \"cutqcut\", \"diff\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"ewma\", \"extract_groups\", \"extract_jsonpath\", \"fused\", \"group_by_list\", \"hash\", \"hex\", \"interpolate\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"jsonpath_lib\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"mode\", \"moment\", \"nightly\", \"object\", \"pct_change\", \"peaks\", \"performant\", \"pivot\", \"polars-json\", \"propagate_nans\", \"rand\", \"rand_distr\", \"random\", \"rank\", \"repeat_by\", \"rle\", \"rolling_window\", \"round_series\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"serde_json\", \"simd\", \"string_encoding\", \"string_pad\", \"string_to_integer\", \"strings\", \"timezones\", \"to_dummies\", \"top_k\", \"unique_counts\", \"zip_with\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 13969717354746350672, "deps": [[5398981501050481332, "version_check", false, 8649128902741779461]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-ops-d3a070524320b0c5/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}