{"rustc": 11410426090777951712, "features": "[\"algorithm_group_by\", \"chrono\", \"comfy-table\", \"docs\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-time\", \"dynamic_group_by\", \"fmt\", \"lazy\", \"rand\", \"rand_distr\", \"random\", \"regex\", \"rows\", \"strings\", \"temporal\", \"zip_with\"]", "declared_features": "[\"algorithm_group_by\", \"arrow-array\", \"arrow_rs\", \"asof_join\", \"avx512\", \"bigidx\", \"checked_arithmetic\", \"chrono\", \"chrono-tz\", \"chunked_ids\", \"comfy-table\", \"dataframe_arithmetic\", \"default\", \"describe\", \"diagonal_concat\", \"docs\", \"docs-selection\", \"dot_product\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"fmt\", \"fmt_no_tty\", \"group_by_list\", \"horizontal_concat\", \"is_first_distinct\", \"is_last_distinct\", \"lazy\", \"ndarray\", \"nightly\", \"object\", \"partition_by\", \"performant\", \"product\", \"python\", \"rand\", \"rand_distr\", \"random\", \"regex\", \"reinterpret\", \"rolling_window\", \"round_series\", \"row_hash\", \"rows\", \"serde\", \"serde-lazy\", \"serde_json\", \"simd\", \"sort_multiple\", \"strings\", \"take_opt_iter\", \"temporal\", \"timezones\", \"unique_counts\", \"zip_with\"]", "target": 16956922054172295634, "profile": 15657897354478470176, "path": 1811465370267273155, "deps": [[966925859616469517, "ahash", false, 4020335100232093750], [1804806304303030865, "xxhash_rust", false, 3299356345809508424], [3419428956812390430, "smartstring", false, 16825798558610601556], [3722963349756955755, "once_cell", false, 17804875059537849432], [4446769819467865508, "build_script_build", false, 15169783821725297943], [4978756888369265998, "polars_utils", false, 8507928368020970212], [5157631553186200874, "num_traits", false, 1532232505406381417], [6493259146304816786, "indexmap", false, 2733906120469181723], [6511429716036861196, "bytemuck", false, 1177516685954284277], [6930142607113040362, "arrow", false, 2698406127485069570], [7896293946984509699, "bitflags", false, 9316908621467576491], [8008191657135824715, "thiserror", false, 1048344762606963720], [9196727883430091646, "rand_distr", false, 17452646904665760930], [9451456094439810778, "regex", false, 6337596358325284129], [9897246384292347999, "chrono", false, 11771647821370589633], [10170320114020213262, "comfy_table", false, 15888787267479523983], [10697383615564341592, "rayon", false, 17457214322954202663], [10766548186203220745, "polars_error", false, 8960065157875073157], [12170264697963848012, "either", false, 6490692808720858043], [13018563866916002725, "hashbrown", false, 10905555190870098526], [13208667028893622512, "rand", false, 5133256524514806048], [14735574642547629702, "polars_row", false, 5917934039257892396]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-core-c9a52a42e091e1f0/dep-lib-polars_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}