{"rustc": 11410426090777951712, "features": "[\"csv\", \"default\", \"docs\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-slim\", \"fmt\", \"lazy\", \"polars-io\", \"polars-lazy\", \"polars-ops\", \"polars-time\", \"strings\", \"temporal\", \"zip_with\"]", "declared_features": "[\"abs\", \"approx_unique\", \"arg_where\", \"array_any_all\", \"asof_join\", \"async\", \"avro\", \"avx512\", \"aws\", \"azure\", \"bench\", \"bigidx\", \"binary_encoding\", \"checked_arithmetic\", \"chunked_ids\", \"cloud\", \"cloud_write\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cumulative_eval\", \"cutqcut\", \"dataframe_arithmetic\", \"date_offset\", \"decompress\", \"decompress-fast\", \"default\", \"describe\", \"diagonal_concat\", \"diff\", \"docs\", \"docs-selection\", \"dot_diagram\", \"dot_product\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-full\", \"dtype-i16\", \"dtype-i8\", \"dtype-slim\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"extract_groups\", \"extract_jsonpath\", \"find_many\", \"fmt\", \"fmt_no_tty\", \"fused\", \"gcp\", \"group_by_list\", \"hist\", \"horizontal_concat\", \"http\", \"interpolate\", \"ipc\", \"ipc_streaming\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"lazy\", \"lazy_regex\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_eval\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"ndarray\", \"nightly\", \"object\", \"parquet\", \"partition_by\", \"pct_change\", \"peaks\", \"performant\", \"pivot\", \"polars-io\", \"polars-lazy\", \"polars-ops\", \"polars-plan\", \"polars-sql\", \"polars-time\", \"product\", \"propagate_nans\", \"random\", \"range\", \"rank\", \"reinterpret\", \"repeat_by\", \"replace\", \"rle\", \"rolling_window\", \"round_series\", \"row_hash\", \"rows\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"serde-lazy\", \"sign\", \"simd\", \"sql\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_reverse\", \"string_to_integer\", \"strings\", \"take_opt_iter\", \"temporal\", \"test\", \"timezones\", \"to_dummies\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\", \"zip_with\"]", "target": 9043257715589065068, "profile": 15657897354478470176, "path": 15752540071652089258, "deps": [[1458751047317679729, "polars_core", false, 963042132233614605], [6715185521598727875, "polars_io", false, 2782284420356540327], [9101573641635189135, "polars_ops", false, 14525502535905877367], [12302415943370065676, "build_script_build", false, 14196898544849502011], [13238314574749385273, "polars_time", false, 17498489232097531914], [15364890382109803616, "polars_lazy", false, 894145490809095121]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-51dcb94d1d8ff8ee/dep-lib-polars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}