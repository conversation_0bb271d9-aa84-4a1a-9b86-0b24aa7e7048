{"rustc": 11410426090777951712, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"default\", \"fresh-rust\", \"nightly\", \"serde\", \"std\"]", "target": 5388200169723499962, "profile": 187265481308423917, "path": 2083047015787331885, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/allocator-api2-7465589f0004212b/dep-lib-allocator_api2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}