{"rustc": 11410426090777951712, "features": "[\"csv\", \"default\", \"docs\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-slim\", \"fmt\", \"lazy\", \"polars-io\", \"polars-lazy\", \"polars-time\", \"strings\", \"temporal\", \"zip_with\"]", "declared_features": "[\"abs\", \"algo\", \"approx_unique\", \"arg_where\", \"asof_join\", \"async\", \"avro\", \"avx512\", \"aws\", \"azure\", \"bench\", \"bigidx\", \"binary_encoding\", \"checked_arithmetic\", \"chunked_ids\", \"cloud\", \"cloud_write\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cumulative_eval\", \"cutqcut\", \"dataframe_arithmetic\", \"date_offset\", \"decompress\", \"decompress-fast\", \"default\", \"describe\", \"diagonal_concat\", \"diff\", \"docs\", \"docs-selection\", \"dot_diagram\", \"dot_product\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-full\", \"dtype-i16\", \"dtype-i8\", \"dtype-slim\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"extract_groups\", \"extract_jsonpath\", \"fmt\", \"fmt_no_tty\", \"fused\", \"gcp\", \"group_by_list\", \"horizontal_concat\", \"http\", \"interpolate\", \"ipc\", \"ipc_streaming\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"lazy\", \"lazy_regex\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_eval\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"ndarray\", \"nightly\", \"object\", \"parquet\", \"partition_by\", \"pct_change\", \"peaks\", \"performant\", \"pivot\", \"polars-algo\", \"polars-io\", \"polars-lazy\", \"polars-sql\", \"polars-time\", \"product\", \"propagate_nans\", \"random\", \"range\", \"rank\", \"reinterpret\", \"repeat_by\", \"rle\", \"rolling_window\", \"round_series\", \"row_hash\", \"rows\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"serde-lazy\", \"sign\", \"simd\", \"sort_multiple\", \"sql\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_to_integer\", \"strings\", \"take_opt_iter\", \"temporal\", \"test\", \"timezones\", \"to_dummies\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\", \"zip_with\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 9300885430881557853, "deps": [[5398981501050481332, "version_check", false, 8649128902741779461]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-00cf150cdabe10f5/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}