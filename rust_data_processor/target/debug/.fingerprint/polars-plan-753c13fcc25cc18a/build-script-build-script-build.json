{"rustc": 11410426090777951712, "features": "[\"csv\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-time\", \"polars-time\", \"strings\", \"temporal\"]", "declared_features": "[\"abs\", \"approx_unique\", \"arg_where\", \"asof_join\", \"async\", \"bigidx\", \"binary_encoding\", \"chrono\", \"chrono-tz\", \"chunked_ids\", \"ciborium\", \"cloud\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cutqcut\", \"date_offset\", \"debugging\", \"diff\", \"dot_diagram\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"extract_groups\", \"extract_jsonpath\", \"ffi_plugin\", \"fmt\", \"fused\", \"future\", \"futures\", \"hive_partitions\", \"interpolate\", \"ipc\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"libloading\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"nightly\", \"object\", \"panic_on_schema\", \"parquet\", \"pct_change\", \"peaks\", \"pivot\", \"polars-ffi\", \"polars-parquet\", \"polars-time\", \"propagate_nans\", \"python\", \"random\", \"range\", \"rank\", \"regex\", \"repeat_by\", \"rle\", \"rolling_window\", \"round_series\", \"row_hash\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"sign\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_to_integer\", \"strings\", \"temporal\", \"timezones\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 17448890627470298779, "deps": [[5398981501050481332, "version_check", false, 8649128902741779461]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-plan-753c13fcc25cc18a/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}