{"rustc": 11410426090777951712, "features": "[\"csv\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-time\", \"polars-time\", \"strings\", \"temporal\"]", "declared_features": "[\"abs\", \"approx_unique\", \"arg_where\", \"asof_join\", \"async\", \"bigidx\", \"binary_encoding\", \"chrono\", \"chrono-tz\", \"chunked_ids\", \"ciborium\", \"cloud\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cutqcut\", \"date_offset\", \"debugging\", \"diff\", \"dot_diagram\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"extract_groups\", \"extract_jsonpath\", \"ffi_plugin\", \"fmt\", \"fused\", \"future\", \"futures\", \"hive_partitions\", \"interpolate\", \"ipc\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"libloading\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"nightly\", \"object\", \"panic_on_schema\", \"parquet\", \"pct_change\", \"peaks\", \"pivot\", \"polars-ffi\", \"polars-parquet\", \"polars-time\", \"propagate_nans\", \"python\", \"random\", \"range\", \"rank\", \"regex\", \"repeat_by\", \"rle\", \"rolling_window\", \"round_series\", \"row_hash\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"sign\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_to_integer\", \"strings\", \"temporal\", \"timezones\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\"]", "target": 11334077754402081686, "profile": 2241668132362809309, "path": 17582501308129811200, "deps": [[40386456601120721, "percent_encoding", false, 3642659130288901092], [963741888112789140, "polars_ops", false, 8828107842929973698], [966925859616469517, "ahash", false, 10174633265036692475], [3419428956812390430, "smartstring", false, 9692665678905392005], [3495776386425160289, "build_script_build", false, 16770138158575400628], [3722963349756955755, "once_cell", false, 6293031810255931828], [4446769819467865508, "polars_core", false, 2656845423498715160], [4978756888369265998, "polars_utils", false, 15069460263289402904], [6511429716036861196, "bytemuck", false, 639825653603922915], [6565397770530258860, "polars_io", false, 9849848454521285204], [6930142607113040362, "arrow", false, 17451091832504977821], [10697383615564341592, "rayon", false, 9537286647097859485], [11411182522717811070, "polars_time", false, 10139809070430461122], [15285011755229917804, "strum_macros", false, 4679427528150290646]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-plan-30aae7255234d44d/dep-lib-polars_plan", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}