{"rustc": 11410426090777951712, "features": "[\"algorithm_group_by\"]", "declared_features": "[\"algorithm_group_by\", \"arrow-array\", \"arrow_rs\", \"asof_join\", \"avx512\", \"bigidx\", \"checked_arithmetic\", \"chrono\", \"chrono-tz\", \"chunked_ids\", \"comfy-table\", \"dataframe_arithmetic\", \"default\", \"describe\", \"diagonal_concat\", \"docs\", \"docs-selection\", \"dot_product\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"fmt\", \"fmt_no_tty\", \"group_by_list\", \"horizontal_concat\", \"is_first_distinct\", \"is_last_distinct\", \"lazy\", \"ndarray\", \"nightly\", \"object\", \"partition_by\", \"performant\", \"product\", \"python\", \"rand\", \"rand_distr\", \"random\", \"regex\", \"reinterpret\", \"rolling_window\", \"round_series\", \"row_hash\", \"rows\", \"serde\", \"serde-lazy\", \"serde_json\", \"simd\", \"sort_multiple\", \"strings\", \"take_opt_iter\", \"temporal\", \"timezones\", \"unique_counts\", \"zip_with\"]", "target": 16956922054172295634, "profile": 15657897354478470176, "path": 1811465370267273155, "deps": [[966925859616469517, "ahash", false, 4020335100232093750], [1804806304303030865, "xxhash_rust", false, 3299356345809508424], [3419428956812390430, "smartstring", false, 16825798558610601556], [3722963349756955755, "once_cell", false, 17804875059537849432], [4446769819467865508, "build_script_build", false, 11006068845932068827], [4978756888369265998, "polars_utils", false, 1073809846642864966], [5157631553186200874, "num_traits", false, 1532232505406381417], [6493259146304816786, "indexmap", false, 2733906120469181723], [6511429716036861196, "bytemuck", false, 1177516685954284277], [6930142607113040362, "arrow", false, 3871603002785080498], [7896293946984509699, "bitflags", false, 9316908621467576491], [8008191657135824715, "thiserror", false, 1048344762606963720], [10697383615564341592, "rayon", false, 17457214322954202663], [10766548186203220745, "polars_error", false, 8034755695549783337], [12170264697963848012, "either", false, 6490692808720858043], [13018563866916002725, "hashbrown", false, 10905555190870098526], [14735574642547629702, "polars_row", false, 2591761526429616226]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-core-8cd5cbc1e11223ee/dep-lib-polars_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}