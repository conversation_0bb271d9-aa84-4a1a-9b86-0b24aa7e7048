{"rustc": 11410426090777951712, "features": "[\"csv\", \"dtype-date\", \"dtype-datetime\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-time\", \"polars-time\", \"strings\", \"temporal\"]", "declared_features": "[\"abs\", \"approx_unique\", \"arg_where\", \"array_any_all\", \"asof_join\", \"async\", \"bigidx\", \"binary_encoding\", \"chrono\", \"chrono-tz\", \"chunked_ids\", \"ciborium\", \"cloud\", \"coalesce\", \"concat_str\", \"cov\", \"cross_join\", \"cse\", \"csv\", \"cum_agg\", \"cutqcut\", \"date_offset\", \"debugging\", \"diff\", \"dot_diagram\", \"dtype-array\", \"dtype-categorical\", \"dtype-date\", \"dtype-datetime\", \"dtype-decimal\", \"dtype-duration\", \"dtype-i16\", \"dtype-i8\", \"dtype-struct\", \"dtype-time\", \"dtype-u16\", \"dtype-u8\", \"dynamic_group_by\", \"ewma\", \"extract_groups\", \"extract_jsonpath\", \"ffi_plugin\", \"find_many\", \"fmt\", \"fused\", \"future\", \"futures\", \"hist\", \"hive_partitions\", \"horizontal_concat\", \"interpolate\", \"ipc\", \"is_first_distinct\", \"is_in\", \"is_last_distinct\", \"is_unique\", \"json\", \"libloading\", \"list_any_all\", \"list_count\", \"list_drop_nulls\", \"list_gather\", \"list_sample\", \"list_sets\", \"list_to_struct\", \"log\", \"merge_sorted\", \"meta\", \"mode\", \"moment\", \"nightly\", \"object\", \"panic_on_schema\", \"parquet\", \"pct_change\", \"peaks\", \"pivot\", \"polars-ffi\", \"polars-json\", \"polars-parquet\", \"polars-time\", \"propagate_nans\", \"python\", \"random\", \"range\", \"rank\", \"regex\", \"repeat_by\", \"replace\", \"rle\", \"rolling_window\", \"round_series\", \"row_hash\", \"search_sorted\", \"semi_anti_join\", \"serde\", \"sign\", \"streaming\", \"string_encoding\", \"string_pad\", \"string_reverse\", \"string_to_integer\", \"strings\", \"temporal\", \"timezones\", \"top_k\", \"trigonometry\", \"true_div\", \"unique_counts\"]", "target": 11334077754402081686, "profile": 2241668132362809309, "path": 1900030615374729713, "deps": [[40386456601120721, "percent_encoding", false, 3642659130288901092], [966925859616469517, "ahash", false, 10174633265036692475], [1458751047317679729, "polars_core", false, 12785791926672904966], [2493041755969688708, "build_script_build", false, 4455948447774639010], [2752773142256470517, "arrow", false, 1554403704926783477], [3419428956812390430, "smartstring", false, 9692665678905392005], [3722963349756955755, "once_cell", false, 6293031810255931828], [6511429716036861196, "bytemuck", false, 639825653603922915], [6643084693527522471, "polars_utils", false, 9121758026600700075], [6715185521598727875, "polars_io", false, 10100491225361608225], [9101573641635189135, "polars_ops", false, 10848656423973692580], [10697383615564341592, "rayon", false, 9537286647097859485], [13238314574749385273, "polars_time", false, 3265851153154620215], [15285011755229917804, "strum_macros", false, 4679427528150290646]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/polars-plan-0f242dbdd700f92b/dep-lib-polars_plan", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}