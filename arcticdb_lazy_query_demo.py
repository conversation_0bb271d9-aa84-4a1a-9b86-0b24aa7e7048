#!/usr/bin/env python3
"""
ArcticDB Lazy Query Demonstration
=================================

This script demonstrates the difference between chunking and timestamp-based lazy queries
for efficient data retrieval from ArcticDB.

Key Benefits of Timestamp-Based Approach:
1. Leverages ArcticDB's native timestamp indexing
2. Reduces memory usage through smaller time-based chunks
3. Better I/O efficiency with storage-layer filtering
4. Maintains data locality for time-series operations
5. Enables true lazy evaluation
"""

import pandas as pd
from arcticdb import Arctic, QueryBuilder
import datetime
from typing import Tuple, List, Optional
import time


class ArcticDBLazyQueryDemo:
    def __init__(self, connection_string: str, library_name: str):
        self.store = Arctic(connection_string)
        self.library_name = library_name
        self.lib = self.store[library_name]
    
    def demonstrate_chunking_vs_timestamp_filtering(self, symbol: str):
        """
        Compare the old chunking approach vs new timestamp-based filtering.
        """
        print(f"=== Comparison Demo for {symbol} ===\n")
        
        # Method 1: Old Chunking Approach (INEFFICIENT)
        print("1. OLD APPROACH: Row-based chunking")
        print("   Problems:")
        print("   - Loads arbitrary row ranges regardless of time")
        print("   - No leverage of timestamp indexing")
        print("   - Poor data locality")
        print("   - Memory inefficient for time-series data")
        
        start_time = time.time()
        try:
            # Simulate old chunking approach
            total_rows = self.lib.get_description(symbol).row_count
            chunk_size = 100000
            
            print(f"   Total rows: {total_rows:,}")
            print(f"   Chunk size: {chunk_size:,}")
            print(f"   Number of chunks: {total_rows // chunk_size + 1}")
            
            # Just demonstrate the concept without actually processing
            print("   Would process chunks: [0:100000], [100000:200000], ...")
            
        except Exception as e:
            print(f"   Error with chunking approach: {e}")
        
        chunking_time = time.time() - start_time
        print(f"   Setup time: {chunking_time:.3f} seconds\n")
        
        # Method 2: New Timestamp-Based Approach (EFFICIENT)
        print("2. NEW APPROACH: Timestamp-based lazy queries")
        print("   Benefits:")
        print("   - Leverages ArcticDB's timestamp indexing")
        print("   - Natural data locality for time-series")
        print("   - Memory efficient monthly chunks")
        print("   - Storage-layer filtering")
        
        start_time = time.time()
        try:
            # Get actual date range
            start_date, end_date = self._get_data_date_range(symbol)
            if start_date and end_date:
                monthly_ranges = self._generate_monthly_ranges(start_date, end_date)
                
                print(f"   Date range: {start_date.date()} to {end_date.date()}")
                print(f"   Monthly chunks: {len(monthly_ranges)}")
                print(f"   Example ranges:")
                for i, (start, end) in enumerate(monthly_ranges[:3]):
                    print(f"     {i+1}. {start.strftime('%Y-%m-%d')} to {end.strftime('%Y-%m-%d')}")
                if len(monthly_ranges) > 3:
                    print(f"     ... and {len(monthly_ranges) - 3} more months")
                    
        except Exception as e:
            print(f"   Error with timestamp approach: {e}")
            
        timestamp_time = time.time() - start_time
        print(f"   Setup time: {timestamp_time:.3f} seconds\n")
        
        print("3. PERFORMANCE COMPARISON:")
        print(f"   Chunking approach setup: {chunking_time:.3f}s")
        print(f"   Timestamp approach setup: {timestamp_time:.3f}s")
        print(f"   Improvement: {((chunking_time - timestamp_time) / chunking_time * 100):.1f}% faster setup")
    
    def demonstrate_lazy_evaluation_with_querybuilder(self, symbol: str, target_month: str = "2020-01"):
        """
        Demonstrate ArcticDB's QueryBuilder for advanced lazy evaluation.
        """
        print(f"\n=== QueryBuilder Lazy Evaluation Demo ===")
        print(f"Symbol: {symbol}, Target Month: {target_month}\n")
        
        try:
            # Parse target month
            year, month = map(int, target_month.split('-'))
            month_start = pd.Timestamp(year=year, month=month, day=1)
            
            if month == 12:
                month_end = pd.Timestamp(year=year+1, month=1, day=1) - pd.Timedelta(nanoseconds=1)
            else:
                month_end = pd.Timestamp(year=year, month=month+1, day=1) - pd.Timedelta(nanoseconds=1)
            
            print("1. STANDARD READ (loads all data):")
            start_time = time.time()
            # Don't actually load all data, just show the concept
            print("   lib.read(symbol).data  # Loads entire dataset!")
            print("   ❌ Memory intensive")
            print("   ❌ Network intensive") 
            print("   ❌ No filtering at storage layer")
            standard_time = time.time() - start_time
            
            print("\n2. TIMESTAMP FILTERING (loads month only):")
            start_time = time.time()
            monthly_data = self.lib.read(symbol, date_range=(month_start, month_end)).data
            timestamp_time = time.time() - start_time
            
            print(f"   lib.read(symbol, date_range=({month_start.date()}, {month_end.date()})).data")
            print(f"   ✓ Loaded {len(monthly_data):,} rows for {target_month}")
            print(f"   ✓ Memory efficient: ~{len(monthly_data) * 8 * 10 / 1024 / 1024:.1f} MB")
            print(f"   ✓ Load time: {timestamp_time:.3f} seconds")
            
            print("\n3. QUERYBUILDER APPROACH (most advanced):")
            start_time = time.time()
            
            # Build query with multiple conditions
            q = QueryBuilder()
            q = q.date_range((month_start, month_end))
            
            # You can add more filters here for even better performance
            # q = q.apply("symbol", lambda x: x.isin(['NIFTY', 'BANKNIFTY']))
            
            querybuilder_data = self.lib.read(symbol, query_builder=q).data
            querybuilder_time = time.time() - start_time
            
            print(f"   q = QueryBuilder().date_range(({month_start.date()}, {month_end.date()}))")
            print(f"   lib.read(symbol, query_builder=q).data")
            print(f"   ✓ Loaded {len(querybuilder_data):,} rows")
            print(f"   ✓ Advanced lazy evaluation")
            print(f"   ✓ Pushes filtering to storage layer")
            print(f"   ✓ Load time: {querybuilder_time:.3f} seconds")
            
            print(f"\n4. PERFORMANCE SUMMARY:")
            print(f"   Timestamp filtering: {timestamp_time:.3f}s")
            print(f"   QueryBuilder: {querybuilder_time:.3f}s")
            
            if len(monthly_data) > 0:
                print(f"\n5. DATA SAMPLE (first 3 rows):")
                print(monthly_data.head(3).to_string())
                
        except Exception as e:
            print(f"Error in QueryBuilder demo: {e}")
    
    def demonstrate_monthly_processing_pattern(self, symbol: str, max_months: int = 3):
        """
        Show the recommended monthly processing pattern.
        """
        print(f"\n=== Monthly Processing Pattern Demo ===")
        print(f"Symbol: {symbol}, Max months to demo: {max_months}\n")
        
        try:
            start_date, end_date = self._get_data_date_range(symbol)
            if not start_date or not end_date:
                print("Could not determine date range")
                return
                
            monthly_ranges = self._generate_monthly_ranges(start_date, end_date)
            
            print("RECOMMENDED PATTERN for large datasets:")
            print("```python")
            print("# 1. Get date range")
            print("start_date, end_date = get_data_date_range(lib, symbol)")
            print("")
            print("# 2. Generate monthly ranges") 
            print("monthly_ranges = generate_monthly_ranges(start_date, end_date)")
            print("")
            print("# 3. Process each month with lazy evaluation")
            print("for month_start, month_end in monthly_ranges:")
            print("    # ArcticDB's lazy evaluation - only loads this month!")
            print("    monthly_data = lib.read(symbol, date_range=(month_start, month_end)).data")
            print("    ")
            print("    # Process monthly chunk (your existing Rust pipeline)")
            print("    processed_data = process_and_resample_chunk(monthly_data)")
            print("    ")
            print("    # Save results")
            print("    save_monthly_results(processed_data, month_start)")
            print("```")
            
            print(f"\nDEMO EXECUTION (processing first {max_months} months):")
            
            total_rows = 0
            total_time = 0
            
            for i, (month_start, month_end) in enumerate(monthly_ranges[:max_months]):
                print(f"\nMonth {i+1}: {month_start.strftime('%Y-%m')}")
                
                start_time = time.time()
                monthly_data = self.lib.read(symbol, date_range=(month_start, month_end)).data
                load_time = time.time() - start_time
                
                total_rows += len(monthly_data)
                total_time += load_time
                
                print(f"  ✓ Loaded {len(monthly_data):,} rows in {load_time:.3f}s")
                print(f"  ✓ Memory usage: ~{len(monthly_data) * 8 * 10 / 1024 / 1024:.1f} MB")
                print(f"  ✓ Date range: {monthly_data['timestamp'].min()} to {monthly_data['timestamp'].max()}")
                
            print(f"\nSUMMARY:")
            print(f"  Total rows processed: {total_rows:,}")
            print(f"  Total time: {total_time:.3f}s")
            print(f"  Average time per month: {total_time/max_months:.3f}s")
            print(f"  Estimated time for all {len(monthly_ranges)} months: {total_time/max_months * len(monthly_ranges):.1f}s")
            
        except Exception as e:
            print(f"Error in monthly processing demo: {e}")
    
    def _get_data_date_range(self, symbol: str) -> Tuple[Optional[pd.Timestamp], Optional[pd.Timestamp]]:
        """Get the date range of data for a symbol."""
        try:
            description = self.lib.get_description(symbol)
            total_rows = description.row_count
            
            # Read first and last few rows to get date range efficiently
            first_chunk = self.lib.read(symbol, row_range=[0, min(1000, total_rows)]).data
            last_chunk = self.lib.read(symbol, row_range=[max(0, total_rows-1000), total_rows]).data
            
            combined_sample = pd.concat([first_chunk, last_chunk])
            
            start_date = combined_sample['timestamp'].min()
            end_date = combined_sample['timestamp'].max()
            
            return start_date, end_date
        except Exception:
            return None, None
    
    def _generate_monthly_ranges(self, start_date: pd.Timestamp, end_date: pd.Timestamp) -> List[Tuple[pd.Timestamp, pd.Timestamp]]:
        """Generate monthly date ranges."""
        current_start = start_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        final_end = end_date
        
        monthly_ranges = []
        
        while current_start <= final_end:
            if current_start.month == 12:
                next_month_start = current_start.replace(year=current_start.year + 1, month=1)
            else:
                next_month_start = current_start.replace(month=current_start.month + 1)
            
            current_end = next_month_start - pd.Timedelta(nanoseconds=1)
            
            if current_end > final_end:
                current_end = final_end
                
            monthly_ranges.append((current_start, current_end))
            current_start = next_month_start
            
            if current_start > final_end:
                break
        
        return monthly_ranges


if __name__ == "__main__":
    # Configuration
    CONNECTION_STRING = "s3://192.168.0.121:9000:arctic-db?access=super&secret=doopersecret"
    LIBRARY_NAME = "nse/1_min/snap_file_jan20_may20/trd_ord"
    DEMO_SYMBOL = "NIFTY"  # Change this to any symbol in your dataset
    
    print("ArcticDB Lazy Query Optimization Demo")
    print("=" * 50)
    
    try:
        demo = ArcticDBLazyQueryDemo(CONNECTION_STRING, LIBRARY_NAME)
        
        # Run all demonstrations
        demo.demonstrate_chunking_vs_timestamp_filtering(DEMO_SYMBOL)
        demo.demonstrate_lazy_evaluation_with_querybuilder(DEMO_SYMBOL)
        demo.demonstrate_monthly_processing_pattern(DEMO_SYMBOL)
        
        print("\n" + "=" * 50)
        print("Demo completed! Key takeaways:")
        print("1. Use timestamp-based queries instead of row-based chunking")
        print("2. Leverage ArcticDB's native lazy evaluation")
        print("3. Process data in natural time-based chunks (monthly)")
        print("4. Use QueryBuilder for advanced filtering scenarios")
        print("5. This approach scales better with data size")
        
    except Exception as e:
        print(f"Demo failed: {e}")
        print("Make sure ArcticDB is accessible and the library/symbol exists")
