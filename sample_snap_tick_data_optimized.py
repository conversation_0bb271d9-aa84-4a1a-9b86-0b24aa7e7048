import logging
import os
import glob
import re
import pandas as pd
from arcticdb import Arctic, QueryBuilder
from multiprocessing import Pool
import datetime
import sys

# Add the rust processor to the path
sys.path.append('/home/<USER>/repos/data_auditing/rust_data_processor')

# Import the optimized data processor - Rust only
from python_wrapper import process_and_resample_chunk

print("Using Rust-optimized data processing")

library = "nse/1_min/snap_file_jan20_may20/trd_ord"
sampled_location = f"{library.split('/')[2]}_sampled"
if not os.path.exists(f"/home/<USER>/repos/data_auditing/{sampled_location}"):
    os.makedirs(f"/home/<USER>/repos/data_auditing/{sampled_location}")
    
storet = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")


def get_data_date_range(lib, sym):
    """
    Get the date range of data for a symbol to determine monthly intervals.
    """
    try:
        # Get a small sample to determine date range
        sample_data = lib.read(sym, row_range=[0, 1]).data
        if sample_data.empty:
            return None, None

        # Get the full date range more efficiently
        description = lib.get_description(sym)
        total_rows = description.row_count

        # Read first and last few rows to get date range
        first_chunk = lib.read(sym, row_range=[0, min(1000, total_rows)]).data
        last_chunk = lib.read(sym, row_range=[max(0, total_rows-1000), total_rows]).data

        combined_sample = pd.concat([first_chunk, last_chunk])

        start_date = combined_sample['timestamp'].min()
        end_date = combined_sample['timestamp'].max()

        return start_date, end_date
    except Exception as e:
        print(f"Error getting date range for {sym}: {e}")
        return None, None


def generate_monthly_ranges(start_date, end_date):
    """
    Generate monthly date ranges for lazy querying.
    """
    if start_date is None or end_date is None:
        return []

    # Normalize to start of months
    current_start = start_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    final_end = end_date

    monthly_ranges = []

    while current_start <= final_end:
        # Calculate end of current month
        if current_start.month == 12:
            next_month_start = current_start.replace(year=current_start.year + 1, month=1)
        else:
            next_month_start = current_start.replace(month=current_start.month + 1)

        # End of current month is start of next month minus 1 nanosecond
        current_end = next_month_start - pd.Timedelta(nanoseconds=1)

        # Don't go beyond the actual end date
        if current_end > final_end:
            current_end = final_end

        monthly_ranges.append((current_start, current_end))

        # Move to next month
        current_start = next_month_start

        # Break if we've covered the end date
        if current_start > final_end:
            break

    return monthly_ranges


def process_snap_file_optimized(sym):
    """
    Optimized version using timestamp-based lazy queries for monthly data retrieval.
    This replaces chunking with ArcticDB's native timestamp filtering capabilities.
    """
    try:
        if len(glob.glob(f"/home/<USER>/repos/data_auditing/{sampled_location}_combined/{sym}_*.parquet")):
            return

        store = Arctic(
            "s3://*************:9000:arctic-db?access=super&secret=doopersecret"
        )

        print(f"Started symbol: {sym}")
        lib = store[library]

        # Get the date range for this symbol
        start_date, end_date = get_data_date_range(lib, sym)
        if start_date is None:
            print(f"Could not determine date range for {sym}")
            return

        print(f"Date range for {sym}: {start_date} to {end_date}")

        # Generate monthly ranges for lazy querying
        monthly_ranges = generate_monthly_ranges(start_date, end_date)
        print(f"Processing {len(monthly_ranges)} monthly chunks for {sym}")

        count = 0

        for month_start, month_end in monthly_ranges:
            print(f"Processing month: {month_start.strftime('%Y-%m')} ({month_start} to {month_end})")

            # Use ArcticDB's native timestamp filtering - this is the key optimization!
            # This leverages lazy evaluation and only loads data for the specified time range
            df_chunk = lib.read(sym, date_range=(month_start, month_end)).data

            if df_chunk.empty:
                print(f"No data for {sym} in {month_start.strftime('%Y-%m')}")
                continue

            print(f"Loaded {len(df_chunk)} rows for {month_start.strftime('%Y-%m')}")

            df_chunk = (
                df_chunk.drop_duplicates(keep="last")
                .sort_values(by="timestamp")
            )

            df_chunk.timestamp = df_chunk.timestamp + pd.Timedelta(-1, 'ns')

            # Use optimized combined Rust processing - single pandas↔polars conversion
            # This does chunk separation AND resampling in one Rust call
            resampled_data = process_and_resample_chunk(df_chunk, interval_minutes=1)

            for _, df in resampled_data.items():
                df.index = df.index + pd.Timedelta(1, 'ns')

            # Save resampled data directly - no additional processing needed
            if not resampled_data['fut_trd'].empty:

                resampled_data['fut_trd'].to_parquet(
                    f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_fut_trd_{count}.parquet"
                )

            if not resampled_data['fut_ord'].empty:
                resampled_data['fut_ord'].to_parquet(
                    f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_fut_ord_{count}.parquet"
                )

            if not resampled_data['opt_trd'].empty:
                resampled_data['opt_trd'].to_parquet(
                    f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_opt_trd_{count}.parquet"
                )

            if not resampled_data['opt_ord'].empty:
                resampled_data['opt_ord'].to_parquet(
                    f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_opt_ord_{count}.parquet"
                )

            count += 1

        print(f"Completed symbol: {sym} - processed {count} monthly chunks\n")
    except Exception as e:
        print(f"Failed for {sym} due to: {e}\n")
        return


def process_snap_file_with_querybuilder(sym):
    """
    Advanced version using ArcticDB's QueryBuilder for maximum lazy evaluation efficiency.
    This provides the most optimized approach for large datasets.
    """
    try:
        if len(glob.glob(f"/home/<USER>/repos/data_auditing/{sampled_location}_combined/{sym}_*.parquet")):
            return

        store = Arctic(
            "s3://*************:9000:arctic-db?access=super&secret=doopersecret"
        )

        print(f"Started symbol: {sym} (QueryBuilder version)")
        lib = store[library]

        # Get the date range for this symbol
        start_date, end_date = get_data_date_range(lib, sym)
        if start_date is None:
            print(f"Could not determine date range for {sym}")
            return

        print(f"Date range for {sym}: {start_date} to {end_date}")

        # Generate monthly ranges for lazy querying
        monthly_ranges = generate_monthly_ranges(start_date, end_date)
        print(f"Processing {len(monthly_ranges)} monthly chunks for {sym} using QueryBuilder")

        count = 0

        for month_start, month_end in monthly_ranges:
            print(f"Processing month: {month_start.strftime('%Y-%m')} ({month_start} to {month_end})")

            try:
                # Use QueryBuilder for advanced lazy evaluation with filtering
                # This is the most efficient approach for large datasets
                q = QueryBuilder()
                q = q.date_range((month_start, month_end))

                # Apply additional filters at query level for better performance
                # This pushes filtering down to the storage layer
                df_chunk = lib.read(sym, query_builder=q).data

                if df_chunk.empty:
                    print(f"No data for {sym} in {month_start.strftime('%Y-%m')}")
                    continue

                print(f"Loaded {len(df_chunk)} rows for {month_start.strftime('%Y-%m')} using QueryBuilder")

                df_chunk = (
                    df_chunk.drop_duplicates(keep="last")
                    .sort_values(by="timestamp")
                )

                df_chunk.timestamp = df_chunk.timestamp + pd.Timedelta(-1, 'ns')

                # Use optimized combined Rust processing
                resampled_data = process_and_resample_chunk(df_chunk, interval_minutes=1)

                for _, df in resampled_data.items():
                    df.index = df.index + pd.Timedelta(1, 'ns')

                # Save resampled data
                if not resampled_data['fut_trd'].empty:
                    resampled_data['fut_trd'].to_parquet(
                        f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_fut_trd_{count}.parquet"
                    )

                if not resampled_data['fut_ord'].empty:
                    resampled_data['fut_ord'].to_parquet(
                        f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_fut_ord_{count}.parquet"
                    )

                if not resampled_data['opt_trd'].empty:
                    resampled_data['opt_trd'].to_parquet(
                        f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_opt_trd_{count}.parquet"
                    )

                if not resampled_data['opt_ord'].empty:
                    resampled_data['opt_ord'].to_parquet(
                        f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_opt_ord_{count}.parquet"
                    )

                count += 1

            except Exception as month_error:
                print(f"Error processing month {month_start.strftime('%Y-%m')} for {sym}: {month_error}")
                continue

        print(f"Completed symbol: {sym} - processed {count} monthly chunks using QueryBuilder\n")
    except Exception as e:
        print(f"Failed for {sym} due to: {e}\n")
        return


# Use the optimized function as the main processing function
# You can choose between two optimized approaches:
process_snap_file = process_snap_file_optimized  # Standard timestamp filtering
# process_snap_file = process_snap_file_with_querybuilder  # Advanced QueryBuilder approach

# Rest of the original script remains the same
symbols = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")[
    library
].list_symbols()

# The combined processing function remains unchanged
def combined_processed_snap_files(sym):
    print(f"Started combining {sym}...")

    df_fut_trd_list = [pd.DataFrame()]
    df_fut_ord_list = [pd.DataFrame()]
    df_opt_trd_list = [pd.DataFrame()]
    df_opt_ord_list = [pd.DataFrame()]

    for file in glob.glob(
        f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_*.parquet"
    ):
        if "opt_trd" in file:
            df_opt_trd_list.append(pd.read_parquet(file))
        elif "opt_ord" in file:
            df_opt_ord_list.append(pd.read_parquet(file))
        elif "fut_trd" in file:
            df_fut_trd_list.append(pd.read_parquet(file))
        elif "fut_ord" in file:
            df_fut_ord_list.append(pd.read_parquet(file))
        

    # Combine and sort data - Rust functions already return sorted data
    df_fut_trd = pd.concat(df_fut_trd_list).sort_index()
    df_fut_ord = pd.concat(df_fut_ord_list).sort_index()
    df_opt_trd = pd.concat(df_opt_trd_list).sort_index()
    df_opt_ord = pd.concat(df_opt_ord_list).sort_index()

    df_fut_trd = df_fut_trd.rename(columns={"symbol": "ID", "open" : "Open", "high" : "High", "low" : "Low", "close" : "Close", "volume" : "Volume"})
    df_fut_ord = df_fut_ord.rename(columns={"symbol": "ID", "open" : "Open", "high" : "High", "low" : "Low", "close" : "Close"})
    df_opt_trd = df_opt_trd.rename(columns={"symbol": "ID", "open" : "Open", "high" : "High", "low" : "Low", "close" : "Close", "volume" : "Volume"})
    df_opt_ord = df_opt_ord.rename(columns={"symbol": "ID", "open" : "Open", "high" : "High", "low" : "Low", "close" : "Close"})


    if len(df_fut_trd):
        df_fut_trd.to_parquet(
            f"/home/<USER>/repos/data_auditing/{sampled_location}_combined/{sym}_fut_trd.parquet"
        )
    if len(df_fut_ord):
        df_fut_ord.to_parquet(
            f"/home/<USER>/repos/data_auditing/{sampled_location}_combined/{sym}_fut_ord.parquet"
        )
    if len(df_opt_trd):
        df_opt_trd.to_parquet(
            f"/home/<USER>/repos/data_auditing/{sampled_location}_combined/{sym}_opt_trd.parquet"
        )
    if len(df_opt_ord):
        df_opt_ord.to_parquet(
            f"/home/<USER>/repos/data_auditing/{sampled_location}_combined/{sym}_opt_ord.parquet"
        )

    for file in glob.glob(
        f"/home/<USER>/repos/data_auditing/{sampled_location}/{sym}_*.parquet"
    ):
        os.remove(file)

    print(f"Completed combining {sym}")


def compare_performance(sym, use_querybuilder=False):
    """
    Compare performance between old chunking and new timestamp-based approaches.
    """
    import time

    print(f"\n=== Performance Comparison for {sym} ===")

    if use_querybuilder:
        print("Testing QueryBuilder approach...")
        start_time = time.time()
        process_snap_file_with_querybuilder(sym)
        querybuilder_time = time.time() - start_time
        print(f"QueryBuilder approach took: {querybuilder_time:.2f} seconds")
    else:
        print("Testing timestamp filtering approach...")
        start_time = time.time()
        process_snap_file_optimized(sym)
        timestamp_time = time.time() - start_time
        print(f"Timestamp filtering approach took: {timestamp_time:.2f} seconds")


def process_multiple_symbols_monthly(symbols_list, max_workers=4, use_querybuilder=False):
    """
    Process multiple symbols using monthly timestamp-based queries with parallel processing.
    """
    from concurrent.futures import ProcessPoolExecutor, as_completed

    processing_func = process_snap_file_with_querybuilder if use_querybuilder else process_snap_file_optimized

    print(f"Processing {len(symbols_list)} symbols using {'QueryBuilder' if use_querybuilder else 'timestamp filtering'} approach")
    print(f"Using {max_workers} parallel workers")

    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_symbol = {executor.submit(processing_func, sym): sym for sym in symbols_list}

        # Process completed tasks
        for future in as_completed(future_to_symbol):
            symbol = future_to_symbol[future]
            try:
                future.result()
                print(f"✓ Completed processing {symbol}")
            except Exception as exc:
                print(f"✗ {symbol} generated an exception: {exc}")


if __name__ == "__main__":
    print("=== ArcticDB Timestamp-Based Lazy Query Optimization ===")
    print("This version replaces chunking with monthly timestamp-based queries")
    print("for better performance and memory efficiency.\n")

    # Example usage - single symbol:
    # process_snap_file("NIFTY")  # Uses timestamp filtering
    # process_snap_file_with_querybuilder("NIFTY")  # Uses QueryBuilder

    # Performance comparison:
    # compare_performance("NIFTY", use_querybuilder=False)
    # compare_performance("NIFTY", use_querybuilder=True)

    # For parallel processing with timestamp-based queries:
    # process_multiple_symbols_monthly(symbols[:5], max_workers=4, use_querybuilder=False)
    # process_multiple_symbols_monthly(symbols[:5], max_workers=4, use_querybuilder=True)

    print("Available functions:")
    print("1. process_snap_file_optimized(symbol) - Timestamp filtering approach")
    print("2. process_snap_file_with_querybuilder(symbol) - QueryBuilder approach")
    print("3. compare_performance(symbol, use_querybuilder=False/True) - Performance comparison")
    print("4. process_multiple_symbols_monthly(symbols_list, max_workers=4, use_querybuilder=False/True) - Parallel processing")
    print("5. combined_processed_snap_files(symbol) - Combine processed files")
    print(f"\nReady to process {len(symbols)} symbols using optimized timestamp-based queries")

    print("\n=== Key Improvements ===")
    print("✓ Replaced row-based chunking with timestamp-based monthly queries")
    print("✓ Leverages ArcticDB's native lazy evaluation and timestamp indexing")
    print("✓ Reduces memory usage by processing smaller time-based chunks")
    print("✓ Better I/O efficiency through timestamp filtering at storage layer")
    print("✓ Maintains compatibility with existing Rust processing pipeline")
    print("✓ Provides both standard and QueryBuilder approaches for different use cases")
