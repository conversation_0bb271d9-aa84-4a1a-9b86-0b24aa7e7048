from io import BytesIO
import multiprocessing
import pickle
from minio import Minio
import datetime
import pandas as pd
import os
from arcticdb import Arctic
from main.tanki import Tanki


MINIO_END_POINT_219 = "*************:11009"
MINIO_ACCESS_KEY = "minioreader"
MINIO_SECRET_KEY = "reasersecret"

minio_client_143 = Minio(
    MINIO_END_POINT_219, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, secure=False
)


sid = pickle.load(
    BytesIO(
        minio_client_143.get_object(
            "commondata",
            "balte_uploads/mapping_dict",
        ).data
    )
)


def combine(sym):
    try:
        if sym not in sid:
            return
        if os.path.exists(
            f"/home/<USER>/repos/data_auditing/success_raw_cash_combining/{sym}"
        ):
            return

        storea = Arctic(
            "s3://*************:9000:arctic-db?access=super&secret=doopersecret"
        )
        storek = Arctic(
            "s3://*************:9000:kivi-arcticdb?access=super&secret=doopersecret"
        )

        dfa = pd.DataFrame()
        dfk = pd.DataFrame()
        dfk_temp = pd.DataFrame()
        dfmiss = pd.DataFrame()

        liba = storea["nse/1_min/raw_cash/trd"]
        libk = storek["nse/1_min/raw_cash/trd"]
        libk_temp = storek["nse/1_min/raw_temp_cash/trd"]

        try:
            dfa = liba.read(
                sym, date_range=(None, datetime.datetime(2025, 1, 16))
            ).data.reset_index()
            dfa["Volume"] = (
                dfa.groupby(dfa.timestamp.dt.date)["Cons_Volume"]
                .diff()
                .fillna(dfa.Cons_Volume)
            )
            dfa = dfa[
                (dfa.timestamp.dt.time > datetime.time(9, 15))
                & (dfa.timestamp.dt.time <= datetime.time(15, 30))
            ]
        except:
            pass
        try:
            dfk = libk.read(sym).data.reset_index()
            dfk["Volume"] = (
                dfk.groupby(dfk.timestamp.dt.date)["Cons_Volume"]
                .diff()
                .fillna(dfk.Cons_Volume)
            )
        except:
            pass
        try:
            dfk_temp = libk_temp.read(sym).data
        except:
            pass
        try:
            dfmiss = pd.read_parquet(
                f"/home/<USER>/repos/data_auditing/raw_cash_20250123/{sym}.parquet"
            ).reset_index()
            dfmiss["Volume"] = (
                dfmiss.groupby(dfmiss.timestamp.dt.date)["Cons_Volume"]
                .diff()
                .fillna(dfmiss.Cons_Volume)
            )
        except:
            pass

        tanki = Tanki(exchange_type="nse", write=True)
        tanki.login(username="mantraraj", password="mantraraj")

        df = pd.concat([dfa, dfk, dfmiss]).drop_duplicates()
        
        if len(df) == 0:
            try:
                # writing new metadata
                tanki["nse/1_min/raw_cash/trd"].write_metadata(sym, dfk_temp)

                # writing new data with new metadata
                tanki["nse/1_min/raw_cash/trd"].write(sym, dfk_temp)

                os.makedirs(
                    f"/home/<USER>/repos/data_auditing/success_raw_cash_combining/{sym}"
                )
                
                if os.path.exists(
                    f"/home/<USER>/repos/data_auditing/failed_raw_cash_combining/{sym}"
                ):
                    os.rmdir(
                        f"/home/<USER>/repos/data_auditing/failed_raw_cash_combining/{sym}"
                    )
                print(F"Done for {sym}")
            except Exception as e:
                if os.path.exists(
                    f"/home/<USER>/repos/data_auditing/raw_cash_backup_kivi_arcticdb/{sym}.parquet"
                ):
                    dfo = pd.read_parquet(f"/home/<USER>/repos/data_auditing/raw_cash_backup_kivi_arcticdb/{sym}.parquet")
                    libk.write(sym, dfo)
                os.makedirs(
                    f"/home/<USER>/repos/data_auditing/failed_raw_cash_combining/{sym}", exist_ok=True
                )
            return
            
        df["Cons_Volume"] = df.groupby(df.timestamp.dt.date)["Volume"].cumsum()
        df = df.set_index("timestamp").sort_index()

        df["symbol"] = df["ID"]
        df["ID"] = sid[sym]
        df["ID"] = df["ID"].astype("uint64")

        df = df[["ID", "symbol", "Open", "High", "Low", "Close", "Vwap", "Cons_Volume"]]

        # applying demerger merger
        libafter = storek["nse/1440_min/after_market/trd"]
        demerger_merger = libafter.read("demerger_merger").data
        different_IDs = demerger_merger[demerger_merger["Symbol"] == sym]

        different_IDs = different_IDs.reset_index()

        for ind in range(len(different_IDs) - 1, -1, -1):
            exdate = different_IDs.iloc[ind].exdate
            df.loc[df.index < exdate, "ID"] = different_IDs.iloc[ind].ID

        if len(dfk_temp):
            df = pd.concat([df, dfk_temp])

        try:
            # deleting old metadata
            libk.delete(sym)

            # writing new metadata
            tanki["nse/1_min/raw_cash/trd"].write_metadata(sym, df)

            # writing new data with new metadata
            tanki["nse/1_min/raw_cash/trd"].write(sym, df)

            os.makedirs(
                f"/home/<USER>/repos/data_auditing/success_raw_cash_combining/{sym}"
            )
            if os.path.exists(
                f"/home/<USER>/repos/data_auditing/failed_raw_cash_combining/{sym}"
            ):
                os.rmdir(
                    f"/home/<USER>/repos/data_auditing/failed_raw_cash_combining/{sym}"
                )
            print(F"Done for {sym}")
        except Exception as e:
            if os.path.exists(
                f"/home/<USER>/repos/data_auditing/raw_cash_backup_kivi_arcticdb/{sym}.parquet"
            ):
                dfo = pd.read_parquet(f"/home/<USER>/repos/data_auditing/raw_cash_backup_kivi_arcticdb/{sym}.parquet")
                libk.write(sym, dfo)
            os.makedirs(
                f"/home/<USER>/repos/data_auditing/failed_raw_cash_combining/{sym}", exist_ok=True
            )
    except Exception as e:
        os.makedirs(
            f"/home/<USER>/repos/data_auditing/failed_raw_cash_combining/{sym}", exist_ok=True
        )


# combine(sym="SBIN")


if __name__ == "__main__":
    # storea = Arctic(
    #     "s3://*************:9000:arctic-db?access=super&secret=doopersecret"
    # )
    # storek = Arctic(
    #     "s3://*************:9000:kivi-arcticdb?access=super&secret=doopersecret"
    # )
    # liba = storea["nse/1_min/raw_cash/trd"]
    # libk = storek["nse/1_min/raw_cash/trd"]
    # libk_temp = storek["nse/1_min/raw_temp_cash/trd"]

    # syms = (
    #     set(liba.list_symbols())
    #     | set(libk.list_symbols())
    #     | set(libk_temp.list_symbols())
    #     | set(
    #         [
    #             s.split(".")[0]
    #             for s in os.listdir(
    #                 "/home/<USER>/repos/data_auditing/raw_cash_20250123"
    #             )
    #         ]
    #     )
    # )
    
    
    # checking for failed syms
    # syms = os.listdir(f"/home/<USER>/repos/data_auditing/failed_raw_cash_combining")
    
    # print(F"Total symbols: {len(syms)}")

    # with multiprocessing.Pool(25) as p:
    #     p.map(combine, syms)
    
    # for sym in syms:
    #     combine(sym=sym)
    
    print()












from operator import mul, truediv
import pickle
import pandas as pd
import numpy as np
import logging
from pathlib import Path
import os
from datetime import datetime
from arcticdb import Arctic
from main.data.utility import get_balte_id
from main.tanki import Tanki
from minio import Minio
from io import BytesIO

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(
            f"cash_data_check_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        ),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger("CashDataChecker")


MINIO_END_POINT_219 = "*************:11009"
MINIO_ACCESS_KEY = "minioreader"
MINIO_SECRET_KEY = "reasersecret"

minio_client_143 = Minio(
    MINIO_END_POINT_219, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, secure=False
)


class CashDataChecker:
    def __init__(
        self,
        arctic_uri,
        library,
        corpact_library,
        demerger_merger_library,
        symbol_change_library,
        basis_adjustment_library,
    ):
        """
        Initialize the Cash Data Checker with ArcticDB libraries.

        Args:
            arctic_uri: ArcticDB connection URI
            library: Library name for cash data
            corpact_library: Library name for corporate action data
            demerger_merger_library: Library name for demerger/merger data
            symbol_change_library: Library name for symbol change data
        """
        self.arctic_uri = arctic_uri
        self.library = library
        self.corpact_library = corpact_library
        self.demerger_merger_library = demerger_merger_library
        self.symbol_change_library = symbol_change_library
        self.basis_adjustment_library = basis_adjustment_library

        self.store = Arctic(arctic_uri)

        logger.info("Loading reference data from ArcticDB...")
        self.load_reference_data()

        self.minio_client = Minio(
            "*************:11009", "minioreader", "reasersecret", secure=False
        )
        self.load_symbol_mappings()

        self.tanki_obj = Tanki(exchange_type="nse")
        self.tanki_obj.login(username="mantraraj", password="mantraraj")

        self.CORPACT_ADJUSTMENT_ACTIONS = {
            "Open": mul,
            "High": mul,
            "Low": mul,
            "Close": mul,
            "Vwap": mul,
            "Cons_Volume": truediv,
            "Next_Cons_Volume": truediv,
            "fut_close": mul,
            "near_month": truediv,
            "next_month": truediv,
            "far_month": truediv,
            "upper_circuit": mul,
            "lower_circuit": mul,
            "Close_wgts": truediv,
            "vol_wgts": mul,
            "open": mul,
            "high": mul,
            "low": mul,
            "close": mul,
            "adj_factor": mul,
            "eod_price": mul,
        }

        self.BASIS_ADJUSTMENT_COLUMNS = ["Open", "High", "Low", "Close", "Vwap"]

        self.nifty_expiry_dict = pickle.loads(
            minio_client_143.get_object(
                "commondata", "balte_uploads/nifty_expiry_dict"
            ).data
        )
        self.banknifty_expiry_dict = pickle.loads(
            minio_client_143.get_object(
                "commondata", "balte_uploads/banknifty_expiry_dict"
            ).data
        )
        self.finnifty_expiry_dct = pickle.loads(
            minio_client_143.get_object(
                "commondata", "balte_uploads/finnifty_expiry_dict"
            ).data
        )
        self.niftynxt50_expiry_dict = pickle.loads(
            minio_client_143.get_object(
                "commondata", "balte_uploads/niftynxt50_expiry_dict"
            ).data
        )
        self.midcpnifty_expiry_dict = pickle.loads(
            minio_client_143.get_object(
                "commondata", "balte_uploads/midcpnifty_expiry_dict"
            ).data
        )
        self.optstk_expiry_dict = pickle.loads(
            minio_client_143.get_object(
                "commondata", "balte_uploads/optstk_expiry_dict"
            ).data
        )

        self.expiry_dict_map = {
            "futidx_fut": {
                "5001": self.nifty_expiry_dict,
                "5002": self.banknifty_expiry_dict,
                "5003": self.finnifty_expiry_dct,
                "5004": self.niftynxt50_expiry_dict,
                "5008": self.midcpnifty_expiry_dict,
                "any": self.optstk_expiry_dict,
            },
            "fut": {
                "any": self.optstk_expiry_dict,
            },
        }

    def load_reference_data(self):
        """Load all reference data from ArcticDB"""
        try:
            # Load corporate action data
            corpact_lib = self.store[self.corpact_library]
            self.corpact_data = corpact_lib.read("corpact").data
            logger.info(
                f"Loaded corporate action data: {len(self.corpact_data)} records"
            )

            # Load demerger/merger data
            demerger_merger_lib = self.store[self.demerger_merger_library]
            self.demerger_merger = demerger_merger_lib.read("demerger_merger").data
            logger.info(
                f"Loaded demerger/merger data: {len(self.demerger_merger)} records"
            )

            # Load symbol change data
            symbol_change_lib = self.store[self.symbol_change_library]
            self.symbol_change = symbol_change_lib.read("symbol_change").data
            logger.info(f"Loaded symbol change data: {len(self.symbol_change)} records")

            # Load basis adjustment data
            basis_adjustment_lib = self.store[self.basis_adjustment_library]
            self.repo_rate = basis_adjustment_lib.read("repo_rate").data
            logger.info(f"Loaded basis adjustment data: {len(self.repo_rate)} records")
        except Exception as e:
            logger.error(f"Error loading reference data from ArcticDB: {str(e)}")
            raise

    def load_symbol_mappings(self):
        """Load symbol_to_balte_id and balte_id_to_symbol mappings from Minio"""
        try:
            logger.info("Loading symbol mappings from Minio")

            symbol_to_balte_id_data = self.minio_client.get_object(
                "commondata", "balte_uploads/mapping_dict"
            ).data

            self.symbol_to_balte_id = pickle.load(BytesIO(symbol_to_balte_id_data))
            logger.info(
                f"Loaded symbol_to_balte_id mapping with {len(self.symbol_to_balte_id)} entries"
            )

            # Create balte_id_to_symbol mapping
            self.balte_id_to_symbol = {}
            for symbol, balte_id in self.symbol_to_balte_id.items():
                self.balte_id_to_symbol[balte_id] = symbol

            logger.info(
                f"Created balte_id_to_symbol mapping with {len(self.balte_id_to_symbol)} entries"
            )

        except Exception as e:
            logger.error(f"Error loading symbol mappings from Minio: {str(e)}")
            self.symbol_to_balte_id = {}
            self.balte_id_to_symbol = {}

    def apply_symbol_change(self, symbol, library):
        symbol_list = self.symbol_change[self.symbol_change["current"] == symbol][
            "symbol"
        ].tolist()

        if len(symbol_list) == 0:
            symbol_list.append(symbol)

        raw_data_list = []
        for sy in symbol_list:
            try:
                raw_data_list.append(self.store[library].read(sy).data)
            except Exception as e:
                logger.error(f"Error reading data for {sy}: {str(e)}")
                continue

        raw_data = pd.concat(raw_data_list).sort_index()
        raw_data["ID"] = self.symbol_to_balte_id[symbol]
        raw_data["ID"] = raw_data["ID"].astype("uint64")
        return raw_data

    def apply_demerger_merger(self, data, symbol):
        if len(data) == 0:
            return data

        different_IDs = self.demerger_merger[self.demerger_merger["Symbol"] == symbol]

        different_IDs = different_IDs.reset_index()

        for ind in range(len(different_IDs) - 1, -1, -1):
            exdate = different_IDs.iloc[ind].exdate
            data.loc[data.index < exdate, "ID"] = different_IDs.iloc[ind].ID

        return data

    def apply_corpact(self, data, symbol):
        if len(data) == 0:
            return data

        corpact_info = self.corpact_data[self.corpact_data["ID"] == int(symbol)]
        data["adj_factor"] = 1

        for index in range(len(corpact_info) - 1, -1, -1):
            adj_factor = corpact_info.iloc[index]["adj_factor"]
            exdate = corpact_info.index[index]
            data.loc[data.index < exdate, "adj_factor"] *= adj_factor

        for col in data.columns:
            if col == "adj_factor":
                continue
            if col in self.CORPACT_ADJUSTMENT_ACTIONS:
                data[col] = self.CORPACT_ADJUSTMENT_ACTIONS[col](
                    data[col], data["adj_factor"]
                )

        return data

    def apply_basis_adjustment(self, data, symbol):
        if len(data) == 0:
            return data

        data["date"] = data.index.normalize()

        reporate_info = self.repo_rate.reindex(
            sorted(set(data.date.unique().tolist() + self.repo_rate.index.to_list()))
        )

        if np.isnan(reporate_info["repo_rate"].iloc[0]):
            reporate_info["repo_rate"].iloc[0] = reporate_info[
                ~reporate_info["repo_rate"].isna()
            ].iloc[0]
        reporate_info["repo_rate"] = reporate_info["repo_rate"].ffill()

        data = data.reset_index()
        data = pd.merge(data, reporate_info, how="left", on=["date"])
        data = data.set_index("timestamp")

        # time to expiry in years
        data["repo_rate"] *= (data.expiry - data.date).dt.days / 365

        for column in self.BASIS_ADJUSTMENT_COLUMNS:
            if column in data:
                data[column] = data[column] * (1 - data["repo_rate"])

        data = data.drop(columns=["date", "repo_rate"])
        return data

    def filter_by_expiry_rank(self, data, expiry_rank, expiry_dict):
        if len(data) == 0:
            return data

        data_columns = data.columns
        # Create expiry dataframe with specified categories
        expiry_df = pd.DataFrame.from_dict(expiry_dict, orient="index")
        expiry_df.index.name = "date"

        data["date"] = pd.to_datetime(data.index.date)
        data["timestamp"] = data.index

        data = data.reset_index(drop=True)
        data = pd.merge(data, expiry_df.reset_index(), how="left", on=["date"])

        data["expiry_rank"] = np.nan
        if expiry_rank == 1:
            data.loc[data["expiry"] == data["near_month"], "expiry_rank"] = 1
        elif expiry_rank == 2:
            data.loc[data["expiry"] == data["next_month"], "expiry_rank"] = 2
        else:
            raise ValueError("Invalid expiry rank")

        data = data[data["expiry_rank"] == expiry_rank]
        columns_to_drop = ["expiry_rank", "date"]
        data = data.drop(
            columns=[col for col in columns_to_drop if col in data.columns]
        )
        data = data.set_index("timestamp")
        data = data[data_columns]
        return data

    def check_corpact_adjustments(self, symbol):
        """
        Check if corporate actions have been correctly applied

        Args:
            data: DataFrame containing cash data
            symbol: Symbol being checked

        Returns:
            DataFrame with issues found
        """
        logger.info(f"Checking corpact events for {symbol or 'all symbols'}")

        lib_syms = self.store[self.library].list_symbols()
        if symbol:
            corpact_cur = self.corpact_data[self.corpact_data["ID"] == int(symbol)]
        else:
            corpact_cur = self.corpact_data.copy()

        if len(corpact_cur) == 0:
            return pd.DataFrame()

        issues = []
        for cur_id, corp in corpact_cur.groupby("ID"):
            if str(cur_id) not in lib_syms:
                logger.info(f"{cur_id} not in library but found in corpact")
                continue

            exch, freq, universe, dtype = self.library.split("/")
            raw_library = f"{exch}/1_min/raw_{universe}/{dtype}"

            curr_symbol = self.balte_id_to_symbol[int(cur_id)].split("_")[0]

            raw_data = self.apply_symbol_change(curr_symbol, raw_library)
            raw_data = self.apply_demerger_merger(raw_data, curr_symbol)
            raw_data = raw_data[raw_data.ID == cur_id]

            raw_data["adj_factor"] = 1
            data = self.store[self.library].read(str(cur_id)).data

            for index in range(len(corp) - 1, -1, -1):
                adj_factor = corp.iloc[index]["adj_factor"]
                exdate = corp.index[index]
                raw_data.loc[raw_data.index < exdate, "adj_factor"] *= adj_factor

            for col in raw_data.columns:
                if col == "adj_factor":
                    continue
                if col in self.CORPACT_ADJUSTMENT_ACTIONS:
                    raw_data[col] = self.CORPACT_ADJUSTMENT_ACTIONS[col](
                        raw_data[col], raw_data["adj_factor"]
                    )

            data = pd.merge(
                data.reset_index(),
                raw_data.reset_index(),
                on=["timestamp"],
                how="inner",
            )
            data = data.round(2)
            issue_found = False
            for col in data.columns:
                if col == "adj_factor":
                    continue
                column = col.split("_")[0]
                if column in self.CORPACT_ADJUSTMENT_ACTIONS:
                    if (
                        abs(data[column + "_x"] - data[column + "_y"]).max() > 0.01
                        and (
                            data.loc[
                                abs(data[column + "_x"] - data[column + "_y"]) > 0.01,
                                "adj_factor",
                            ]
                            != 1
                        ).any()
                    ):
                        logger.info(
                            f"Large difference (0.001) found for {column} for {cur_id}"
                        )
                        issue_found = True

            if issue_found:
                issues.append(
                    {
                        "symbol": cur_id,
                        "issue": "Large difference found in corpact adjustments",
                    }
                )

        issues_df = pd.DataFrame(issues)
        return issues_df

    def corpact_fixer(self, symbol_list=None):
        skipped = []
        logger.info("Fixing corpact issues")

        symbol_list = [int(sym) for sym in symbol_list]

        lib_syms = self.store[self.library].list_symbols()
        if symbol_list:
            corpact_current = self.corpact_data[
                self.corpact_data["ID"].isin(symbol_list)
            ]
        else:
            corpact_current = self.corpact_data.copy()

        for cur_id, corp in corpact_current.groupby("ID"):
            if str(cur_id) not in lib_syms:
                logger.info(f"{cur_id} not in library but found in corpact")
                continue

            exch, freq, universe, dtype = self.library.split("/")
            raw_library = f"{exch}/1_min/raw_{universe}/{dtype}"

            curr_symbol = self.balte_id_to_symbol[int(cur_id)].split("_")[0]

            raw_data = self.apply_symbol_change(curr_symbol, raw_library)
            raw_data = self.apply_demerger_merger(raw_data, curr_symbol)
            raw_data = raw_data[raw_data.ID == cur_id]

            raw_data["adj_factor"] = 1
            data = self.store[self.library].read(str(cur_id)).data

            ## applying corpact
            for index in range(len(corp) - 1, -1, -1):
                adj_factor = corp.iloc[index]["adj_factor"]
                exdate = corp.index[index]
                raw_data.loc[raw_data.index < exdate, "adj_factor"] *= adj_factor

            for col in raw_data.columns:
                if col == "adj_factor":
                    continue
                if col in self.CORPACT_ADJUSTMENT_ACTIONS:
                    raw_data[col] = self.CORPACT_ADJUSTMENT_ACTIONS[col](
                        raw_data[col], raw_data["adj_factor"]
                    )

            ## backing up old data
            if not os.path.exists(
                f"/home/<USER>/repos/data_auditing/cash_data_fixer/backup/{self.library}"
            ):
                os.makedirs(
                    f"/home/<USER>/repos/data_auditing/cash_data_fixer/backup/{self.library}"
                )
            data.to_parquet(
                f"/home/<USER>/repos/data_auditing/cash_data_fixer/backup/{self.library}/{cur_id}.parquet"
            )

            if len(data) != len(raw_data):
                logger.info(
                    f"Length of data and raw_data is not same for {cur_id}, skip fixing it"
                )
                skipped.append(
                    {
                        "symbol": cur_id,
                        "issue": "Length of data and raw_data is not same, skip fixing it",
                    }
                )
                continue
            try:
                self.store[self.library].delete(str(cur_id))
                self.tanki_obj[self.library].write_metadata(str(cur_id), raw_data)
                self.tanki_obj[self.library].write(
                    str(cur_id), raw_data, comment="Fixed demerger/merger event"
                )
            except Exception as e:
                logger.error(f"Error writing data for {cur_id}: {str(e)}")

        return skipped

    def check_demerger_merger(self, symbol):
        """
        Check if demerger/merger events have been correctly handled

        Args:
            data: DataFrame containing cash data
            symbol: Symbol being checked

        Returns:
            DataFrame with issues found
        """
        logger.info(f"Checking demerger/merger events for {symbol or 'all symbols'}")

        lib_syms = self.store[self.library].list_symbols()
        if symbol:
            dem_mer_current = self.demerger_merger[
                self.demerger_merger["ID"] == int(symbol)
            ]
        else:
            dem_mer_current = self.demerger_merger.copy()

        if len(dem_mer_current) == 0:
            return pd.DataFrame()

        issues = []
        for exdate, dem_mer in dem_mer_current.iterrows():
            if str(dem_mer["ID"]) not in lib_syms:
                continue
            metadata = (
                self.store[self.library].read_metadata(str(dem_mer["ID"])).metadata
            )
            end_date = pd.Timestamp(metadata["last_timestamp"]).normalize()
            if end_date >= exdate:
                issues.append(
                    {
                        "symbol": dem_mer["ID"],
                        "exdate": exdate,
                        "data_end_date": end_date,
                        "issue": "Data exists after demerger/merger date",
                    }
                )

        issues_df = pd.DataFrame(issues)
        logger.info(f"Found {len(issues_df)} demerger/merger issues")
        return issues_df

    def demerger_merger_fixer(self, symbol_list=None):
        logger.info("Fixing demerger/merger events")

        symbol_list = [int(sym) for sym in symbol_list]

        lib_syms = self.store[self.library].list_symbols()
        if symbol_list:
            dem_mer_current = self.demerger_merger[
                self.demerger_merger["ID"].isin(symbol_list)
            ]
        else:
            dem_mer_current = self.demerger_merger.copy()

        for exdate, dem_mer in dem_mer_current.iterrows():
            if str(dem_mer["ID"]) not in lib_syms:
                continue
            meta_obj = self.store[self.library].read_metadata(str(dem_mer["ID"]))
            metadata = meta_obj.metadata
            end_date = pd.Timestamp(metadata["last_timestamp"]).normalize()

            if end_date >= exdate:
                obj = self.store[self.library].read(str(dem_mer["ID"]))
                data = obj.data

                new_symbol_id = self.symbol_to_balte_id[dem_mer["Symbol"]]
                new_data = data[data.index >= exdate]
                new_data["ID"] = int(new_symbol_id)
                new_data["ID"] = new_data["ID"].astype("uint64")

                data = data[data.index < exdate]

                ## create backup for current data
                if not os.path.exists(
                    f"/home/<USER>/repos/data_auditing/backup/{self.library}"
                ):
                    os.makedirs(
                        f"/home/<USER>/repos/data_auditing/backup/{self.library}"
                    )
                data.to_parquet(
                    f"/home/<USER>/repos/data_auditing/backup/{self.library}/{dem_mer['ID']}.parquet"
                )
                logger.info(f"Created backup for {dem_mer['ID']}")

                try:
                    self.store[self.library].delete(str(dem_mer["ID"]))
                    self.tanki_obj[self.library].write_metadata(
                        str(dem_mer["ID"]), data
                    )
                    self.tanki_obj[self.library].write(
                        str(dem_mer["ID"]), data, comment="Fixed demerger/merger event"
                    )
                except Exception as e:
                    logger.error(f"Error writing data for {dem_mer['ID']}: {str(e)}")

                if str(new_symbol_id) in lib_syms:
                    existing_new_data = (
                        self.store[self.library].read(str(new_symbol_id)).data
                    )

                    ## create backup for new data
                    if not os.path.exists(
                        f"/home/<USER>/repos/data_auditing/backup/{self.library}"
                    ):
                        os.makedirs(
                            f"/home/<USER>/repos/data_auditing/backup/{self.library}"
                        )
                    existing_new_data.to_parquet(
                        f"/home/<USER>/repos/data_auditing/backup/{self.library}/{new_symbol_id}.parquet"
                    )
                    logger.info(f"Created backup for {new_symbol_id}")

                    try:
                        self.store[self.library].delete(str(new_symbol_id))
                        new_data = pd.concat([existing_new_data, new_data]).sort_index()
                        self.tanki_obj[self.library].write_metadata(
                            str(new_symbol_id), new_data
                        )
                        self.tanki_obj[self.library].write(
                            str(new_symbol_id),
                            new_data,
                            comment="Fixed demerger/merger event",
                        )
                    except Exception as e:
                        logger.error(
                            f"Error writing data for {new_symbol_id}: {str(e)}"
                        )

                else:
                    self.tanki_obj[self.library].write_metadata(
                        str(new_symbol_id), new_data
                    )
                    self.tanki_obj[self.library].write(
                        str(new_symbol_id),
                        new_data,
                        comment="Fixed demerger/merger event",
                    )

                logger.info(
                    f"Fixed demerger/merger event for {dem_mer['ID']} to {new_symbol_id}"
                )

    def check_symbol_changes(self, symbol=None):
        """
        Check if symbol changes have been correctly applied

        Args:
            data: DataFrame containing cash data
            symbol: Symbol being checked

        Returns:
            DataFrame with issues found
        """
        logger.info("Checking symbol changes")

        lib_syms = self.store[self.library].list_symbols()
        if symbol:
            curr_symbol = self.balte_id_to_symbol[int(symbol)].split("_")[0]
            symbol_change_cur = self.symbol_change[
                self.symbol_change.current == curr_symbol
            ]
        else:
            symbol_change_cur = self.symbol_change.copy()

        if len(symbol_change_cur) == 0:
            return pd.DataFrame()

        exch, freq, universe, dtype = self.library.split("/")
        raw_library = f"{exch}/1_min/raw_{universe}/{dtype}"

        issues = []
        for cur_sym in symbol_change_cur.current.unique():
            cur_id = self.symbol_to_balte_id[cur_sym]
            raw_data = self.apply_symbol_change(cur_sym, raw_library)
            raw_data = self.apply_demerger_merger(raw_data, cur_sym)
            raw_data = raw_data[raw_data.ID == cur_id]

            data = self.store[self.library].read(str(cur_id)).data

            if len(data) != len(raw_data):
                logger.info(
                    f"Symbol change failed for {cur_id}, length of data and raw_data is not same"
                )
                issues.append(
                    {
                        "symbol": cur_id,
                        "issue": "Length of data and raw_data is not same",
                    }
                )

        issues_df = pd.DataFrame(issues)
        logger.info(f"Found {len(issues_df)} symbol change issues")
        return issues_df

    def symbol_change_fixer(self, symbol_list):
        logger.info("Fixing symbol change issues")

        symbol_list = [int(sym) for sym in symbol_list]

        lib_syms = self.store[self.library].list_symbols()
        symbol_change_cur = self.symbol_change["current"].isin(symbol_list)

        exch, freq, universe, dtype = self.library.split("/")
        raw_library = f"{exch}/1_min/raw_{universe}/{dtype}"

        for cur_sym in symbol_change_cur.current.unique():
            cur_id = self.symbol_to_balte_id[cur_sym]
            raw_data = self.apply_symbol_change(cur_sym, raw_library)
            raw_data = self.apply_demerger_merger(raw_data, cur_sym)
            raw_data = raw_data[raw_data.ID == cur_id]

            raw_data = self.apply_corpact(raw_data, cur_id)

            data = self.store[self.library].read(str(cur_id)).data

            ## backup old data
            if not os.path.exists(
                f"/home/<USER>/repos/data_auditing/cash_data_fixer/backup/{self.library}"
            ):
                os.makedirs(
                    f"/home/<USER>/repos/data_auditing/cash_data_fixer/backup/{self.library}"
                )
            data.to_parquet(
                f"/home/<USER>/repos/data_auditing/cash_data_fixer/backup/{self.library}/{cur_id}.parquet"
            )
            logger.info(f"Created backup for {cur_id}")

            try:
                self.store[self.library].delete(str(cur_id))
                self.tanki_obj[self.library].write_metadata(str(cur_id), raw_data)
                self.tanki_obj[self.library].write(
                    str(cur_id), raw_data, comment="Fixed symbol change"
                )
            except Exception as e:
                logger.error(f"Error writing data for {cur_id}: {str(e)}")

    def check_basis_adjustments(self, symbol=None):
        """
        Check if basis adjustments have been correctly applied

        Args:
            data: DataFrame containing cash data
            symbol: Symbol being checked

        Returns:
            DataFrame with issues found
        """
        logger.info("Checking basis adjustments")

        if symbol:
            symbols = [symbol]
        else:
            symbols = self.store[self.library].list_symbols()

        exch, freq, universe, dtype = self.library.split("/")
        raw_library = f"{exch}/1_min/raw_{universe}/{dtype}"

        issues = []
        for cur_id in symbols:
            cur_sym = self.balte_id_to_symbol[int(cur_id)].split("_")[0]
            raw_data = self.apply_symbol_change(cur_sym, raw_library)
            raw_data = self.apply_demerger_merger(raw_data, cur_sym)
            raw_data = raw_data[raw_data.ID == int(cur_id)]
            raw_data = self.filter_by_expiry_rank(
                raw_data,
                1,
                self.expiry_dict_map[universe].get(
                    cur_sym, self.expiry_dict_map[universe]["any"]
                ),
            )

            if universe == "fut":
                raw_data = self.apply_corpact(raw_data, cur_id)
            raw_data = self.apply_basis_adjustment(raw_data, cur_id)

            data = self.store[self.library].read(str(cur_id)).data

            if len(data) != len(raw_data):
                logger.info(
                    f"Basis adjustment failed for {cur_id}, length of data and raw_data is not same"
                )
                issues.append(
                    {
                        "symbol": cur_id,
                        "issue": "Length of data and raw_data is not same",
                    }
                )
                continue

            data = pd.merge(
                data.reset_index(),
                raw_data.reset_index(),
                how="inner",
                on=["timestamp", "expiry"],
            )
            data = data.round(2)

            for col in self.BASIS_ADJUSTMENT_COLUMNS:
                if col not in data:
                    continue
                if abs(data[col + "_x"] - data[col + "_y"]).max() > 0.01:
                    logger.info(
                        f"Basis adjustment failed for {cur_id}, {col} is not same"
                    )
                    issues.append(
                        {
                            "symbol": cur_id,
                            "issue": f"{col} is not same",
                        }
                    )
                    break
        issues_df = pd.DataFrame(issues)
        logger.info(f"Found {len(issues_df)} basis adjustment issues")
        return issues_df

    def basis_adjustment_fixer(self, symbol_list):
        logger.info("Will add support soon in tanki to compile data symbol wise")
        issues = []
        for sym in symbol_list:
            issues.append(
                {
                    "symbol": sym,
                    "issue": "Failed to fix, will add support to compile data symbol wise in tanki",
                }
            )

        issues_df = pd.DataFrame(issues)
        logger.info(f"Failed to fix {len(issues_df)} basis adjustment issues")
        return issues_df

    def check_metadata(self, symbol=None):
        """
        Check metadata for symbols

        Args:
            symbol: Optional specific symbol to check

        Returns:
            DataFrame with issues found
        """
        pass

    def run_all_checks(
        self, symbol=None, checks=None, ignore_checks=None, fix_checks=None
    ):
        """
        Run all checks on the data

        If symbol is provided, checks only that symbol.
        Otherwise, iterates through all symbols in the library and checks each one.
        Only logs and saves issues for symbols with problems.

        Args:
            symbol: Optional specific symbol to check

        Returns:
            Dictionary of DataFrames with issues found
        """

        logger.info(f"Running checks for symbol: {symbol or 'all symbols'}")

        results = {}
        if ignore_checks is None:
            ignore_checks = []
        if checks is None:
            checks = ["demerger_merger", "corpact", "symbol_change", "basis_adjustment"]

        if "demerger_merger" not in ignore_checks and "demerger_merger" in checks:
            results["demerger_merger_issues"] = self.check_demerger_merger(symbol)

        if "corpact" not in ignore_checks and "corpact" in checks:
            results["corpact_issues"] = self.check_corpact_adjustments(symbol)

        if "symbol_change" not in ignore_checks and "symbol_change" in checks:
            results["symbol_change_issues"] = self.check_symbol_changes(symbol)

        if "basis_adjustment" not in ignore_checks and "basis_adjustment" in checks:
            results["basis_adjustment_issues"] = self.check_basis_adjustments(symbol)

        if fix_checks is None:
            fix_checks = []

        if "demerger_merger" in fix_checks:
            if len(results["demerger_merger_issues"]) > 0:
                logger.info("Fixing demerger/merger issues")
                self.demerger_merger_fixer(
                    results["demerger_merger_issues"]["symbol"].unique().tolist()
                )
        if "corpact" in fix_checks:
            if len(results["corpact_issues"]) > 0:
                logger.info("Fixing corpact issues")
                results["failed_corpact_symbols"] = self.corpact_fixer(
                    results["corpact_issues"]["symbol"].unique().tolist()
                )
        if "symbol_change" in fix_checks:
            if len(results["symbol_change_issues"]) > 0:
                logger.info("Fixing symbol change issues")
                self.symbol_change_fixer(
                    results["symbol_change_issues"]["symbol"].unique().tolist()
                )
        if "basis_adjustment" in fix_checks:
            if len(results["basis_adjustment_issues"]) > 0:
                logger.info("Fixing basis adjustment issues")
                results[
                    "failed_basis_adjustment_symbols"
                ] = self.basis_adjustment_fixer(
                    results["basis_adjustment_issues"]["symbol"].unique().tolist()
                )

        return results


if __name__ == "__main__":
    import argparse

    # Set debug mode
    DEBUG = True  # Set to False for normal execution

    if not DEBUG:
        parser = argparse.ArgumentParser(
            description="Check cash data for various issues"
        )
        parser.add_argument(
            "--arctic_uri",
            default="s3://*************:9000:kivi-arcticdb?access=super&secret=doopersecret",
            help="ArcticDB connection URI",
        )
        parser.add_argument(
            "--library", required=True, help="Library name for cash data"
        )
        parser.add_argument(
            "--corpact_library",
            default="nse/1440_min/after_market/trd",
            help="Library name for corporate action data",
        )
        parser.add_argument(
            "--demerger_merger_library",
            default="nse/1440_min/after_market/trd",
            help="Library name for demerger/merger data",
        )
        parser.add_argument(
            "--symbol_change_library",
            default="nse/1440_min/after_market/trd",
            help="Library name for symbol change data",
        )
        parser.add_argument("--symbol", help="Optional specific symbol to check")
        args = parser.parse_args()
    else:
        # Hardcoded debug values
        args = argparse.Namespace(
            arctic_uri="s3://*************:9000:kivi-arcticdb?access=super&secret=doopersecret",
            library="nse/1_min/cash/trd",
            corpact_library="nse/1440_min/after_market/trd",
            demerger_merger_library="nse/1440_min/after_market/trd",
            symbol_change_library="nse/1440_min/after_market/trd",
            basis_adjustment_library="nse/1440_min/after_market/trd",
            symbol=None,
        )

    checker = CashDataChecker(
        arctic_uri=args.arctic_uri,
        library=args.library,
        corpact_library=args.corpact_library,
        demerger_merger_library=args.demerger_merger_library,
        symbol_change_library=args.symbol_change_library,
        basis_adjustment_library=args.basis_adjustment_library,
    )

    results = checker.run_all_checks(args.symbol, ignore_checks=["basis_adjustment"])

    # Print summary
    if results:
        total_issues = sum(len(df) for df in results.values())
        logger.info(f"Total issues found: {total_issues}")
        for check_name, issues_df in results.items():
            if not issues_df.empty:
                logger.info(f"{check_name}: {len(issues_df)} issues\n{issues_df}")
